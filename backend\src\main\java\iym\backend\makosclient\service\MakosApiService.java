package iym.backend.makosclient.service;

import iym.common.enums.ResultCode;
import iym.common.model.api.Response;
import iym.makos.api.client.gen.api.HealthCheckControllerApi;
import iym.makos.api.client.gen.api.IymDomainSorguControllerApi;
import iym.makos.api.client.gen.api.MahkemeKararTalepControllerApi;
import iym.makos.api.client.gen.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.io.File;

/**
 * MAKOS API Service
 * Generated API client'ı kullanarak MAKOS işlemlerini gerçekleştirir
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MakosApiService {
    private final MahkemeKararTalepControllerApi mahkemeKararTalepControllerApi;
    private final HealthCheckControllerApi healthCheckControllerApi;
    private final IymDomainSorguControllerApi iymDomainSorguControllerApi;

    /**
     * Health check işlemi
     *
     * @return String response
     */
    public HealthCheckResponse healthCheck() {
        try {
            log.info("Performing health check");
            HealthCheckResponse response = healthCheckControllerApi.healthCheck();
            log.info("Health check successful: {}", response);
            return response;
        } catch (RestClientException e) {
            log.error("Health check failed: {}", e.getMessage(), e);
            throw new RuntimeException("Health check failed", e);
        }
    }

    /**
     * Health check with basic authorization işlemi
     *
     * @return String response
     */
    public HealthCheckResponse healthCheckAuthorized() {
        try {
            log.info("Performing health check with basic authorization");
            HealthCheckResponse response = healthCheckControllerApi.healthCheckAuthorized();
            log.info("Health check with basic authorization successful: {}", response);
            return response;
        } catch (RestClientException e) {
            log.error("Health check with basic authorization failed: {}", e.getMessage(), e);
            throw new RuntimeException("Health check with basic authorization failed", e);
        }
    }

    /**
     * Mahkeme bilgisi güncelleme işlemi
     *
     * @param mahkemeKararDosyasi Mahkeme karar dosyası
     * @param mahkemeKararDetay   Mahkeme karar detayları
     * @return IDMahkemeKararGuncellemeResponse response
     */
    public IDMahkemeKararGuncellemeResponse mahkemeBilgisiGuncelle(File mahkemeKararDosyasi, IDMahkemeKararGuncellemeRequest mahkemeKararDetay) {
        try {
            log.info("Updating mahkeme bilgisi for request ID: {}", mahkemeKararDetay.getId());
            IDMahkemeKararGuncellemeResponse response = mahkemeKararTalepControllerApi.mahkemeBilgisiGuncelle(mahkemeKararDosyasi, mahkemeKararDetay);
            log.info("Mahkeme bilgisi update successful for ID: {}", mahkemeKararDetay.getId());
            return response;
        } catch (RestClientException e) {
            log.error("Mahkeme bilgisi update failed for ID {}: {}", mahkemeKararDetay.getId(), e.getMessage(), e);
            throw new RuntimeException("Mahkeme bilgisi update failed", e);
        }
    }

    /**
     * ID tipi karar gönderme işlemi
     *
     * @param mahkemeKararDosyasiID Karar dosyası
     * @param mahkemeKararDetayID   Karar detayları
     * @return IDYeniKararResponse response
     */
    public Response<IDYeniKararResponse> kararGonderID(File mahkemeKararDosyasiID, IDYeniKararRequest mahkemeKararDetayID) {
        try {
            log.info("Sending ID karar for request ID: {}", mahkemeKararDetayID.getId());
            IDYeniKararResponse apiResponse = mahkemeKararTalepControllerApi.yeniKararID(mahkemeKararDosyasiID, mahkemeKararDetayID);
            log.info("ID karar send successful for ID: {}", mahkemeKararDetayID.getId());
            return new Response<>(apiResponse);
        } catch (RestClientException e) {
            log.error("ID karar send failed for ID {}: {}", mahkemeKararDetayID.getId(), e.getMessage(), e);
            return new Response<>(ResultCode.FAILED, e.getMessage());
        }
    }

    /**
     * IT tipi karar gönderme işlemi
     *
     * @param mahkemeKararDosyasiIT Karar dosyası
     * @param mahkemeKararDetayIT   Karar detayları
     * @return ITKararResponse response
     */
    public ITKararResponse kararGonderIT(File mahkemeKararDosyasiIT, ITKararRequest mahkemeKararDetayIT) {
        try {
            log.info("Sending IT karar for request ID: {}", mahkemeKararDetayIT.getId());
            ITKararResponse response = mahkemeKararTalepControllerApi.yenikararIT(mahkemeKararDosyasiIT, mahkemeKararDetayIT);
            log.info("IT karar send successful for ID: {}", mahkemeKararDetayIT.getId());
            return response;
        } catch (RestClientException e) {
            log.error("IT karar send failed for ID {}: {}", mahkemeKararDetayIT.getId(), e.getMessage(), e);
            throw new RuntimeException("IT karar send failed", e);
        }
    }

    /**
     * Hedef ad soyad güncelleme işlemi
     *
     * @param mahkemeKararDosyasi Mahkeme karar dosyası
     * @param mahkemeKararDetay   Mahkeme karar detayları
     * @return IDHedefGuncellemeResponse response
     */
    public IDHedefGuncellemeResponse hedefAdSoyadGuncelle(File mahkemeKararDosyasi, IDHedefGuncellemeRequest mahkemeKararDetay) {
        try {
            log.info("Updating hedef ad soyad for request ID: {}", mahkemeKararDetay.getId());
            IDHedefGuncellemeResponse response = mahkemeKararTalepControllerApi.hedefBilgisiGuncelle(mahkemeKararDosyasi, mahkemeKararDetay);
            log.info("Hedef ad soyad update successful for ID: {}", mahkemeKararDetay.getId());
            return response;
        } catch (RestClientException e) {
            log.error("Hedef ad soyad update failed for ID {}: {}", mahkemeKararDetay.getId(), e.getMessage(), e);
            throw new RuntimeException("Hedef ad soyad update failed", e);
        }
    }


    /**
     * Aidiyat bilgisi güncelleme işlemi
     *
     * @param mahkemeKararDosyasi Mahkeme karar dosyası
     * @param mahkemeKararDetay   Mahkeme karar detayları
     * @return IDAidiyatBilgisiGuncellemeResponse response
     */
    public IDAidiyatBilgisiGuncellemeResponse aidiyatBilgisiGuncelle(File mahkemeKararDosyasi, IDAidiyatBilgisiGuncellemeRequest mahkemeKararDetay) {
        try {
            log.info("Updating aidiyat bilgisi for request ID: {}", mahkemeKararDetay.getId());
            IDAidiyatBilgisiGuncellemeResponse response = mahkemeKararTalepControllerApi.aidiyatBilgisiGuncelle(mahkemeKararDosyasi, mahkemeKararDetay);
            log.info("Aidiyat bilgisi update successful for ID: {}", mahkemeKararDetay.getId());
            return response;
        } catch (RestClientException e) {
            log.error("Aidiyat bilgisi update failed for ID {}: {}", mahkemeKararDetay.getId(), e.getMessage(), e);
            throw new RuntimeException("Aidiyat bilgisi update failed", e);
        }
    }

    /**
     * İl ilçe kodları listesi
     *
     * @return List of IllerDTO response
     */
    public Response<IllerResponse> iller() {
        IllerResponse response = iymDomainSorguControllerApi.iller();
        return new Response<>(response);
    }
//

    /**
     * Kurum kodları listesi
     *
     * @return List of EvrakGelenKurumlarDTO response
     */
    public Response<EvrakGelenKurumlarResponse> kurumlar() {
        log.info("Fetching kurumlar list");
        EvrakGelenKurumlarResponse response = iymDomainSorguControllerApi.kurumlar();
        return new Response<>(response);
    }
//

    /**
     * Mahkeme kodları listesi
     *
     * @return MahkemeKodlariResponse response
     */
    public Response<MahkemeKodlariResponse> mahkemeKodlari() {
        log.info("Fetching mahkeme kodlari list");
        MahkemeKodlariResponse response = iymDomainSorguControllerApi.mahkemeKodlari();
        log.info("Mahkeme kodlari list fetched successfully. Size: {}", response.getMahkemeKodListesi() != null ? response.getMahkemeKodListesi().size() : 0);
        return new Response<>(response);
    }
//

    /**
     * Suc tipleri listesi
     *
     * @return List of suc tipleri response
     */
    public Response<SucTipleriResponse> sucTipleri() {
        log.info("Fetching suc tipleri list");
        SucTipleriResponse response = iymDomainSorguControllerApi.sucTipleri();
        return new Response<>(response);
    }

    /**
     * Sorgu tipleri listesi
     *
     * @return List of sorgu tipleri response
     */
    public Response<SorguTipiListResponse> sorguTipleri() {
        log.info("Fetching sorgu tipleri list");
        SorguTipiListResponse response = iymDomainSorguControllerApi.sorguTipleri();
        return new Response<>(response);
    }

    /**
     * Mahkeme karar tipleri listesi
     *
     * @return List of mahkeme karar tipleri response
     */
    public Response<MahkemeKararTipleriResponse> mahkemeKararTipleri() {
        log.info("Fetching mahkeme karar tipleri list");
        MahkemeKararTipleriResponse response = iymDomainSorguControllerApi.mahkemeKararTipleri();
        return new Response<>(response);
    }

    /**
     * Tespit turleri listesi
     *
     * @return List of tespit turleri response
     */
    public Response<TespitTuruListResponse> tespitTurleri() {
        log.info("Fetching tespit turleri list");
        TespitTuruListResponse response = iymDomainSorguControllerApi.tespitTurleri();
        return new Response<>(response);
    }

    /**
     * Mahkeme karar talep sorgulama işlemi
     *
     * @param request Sorgulama parametreleri
     * @return IDMahkemeKararTalepSorgulamaResponse response
     */
    public IDMahkemeKararTalepSorgulamaResponse mahkemeKararTalepSorgu(IDMahkemeKararTalepSorgulamaRequest request) {
        try {
            log.info("Performing mahkeme karar talep sorgu for request ID: {}", request.getId());
            IDMahkemeKararTalepSorgulamaResponse response = mahkemeKararTalepControllerApi.mahkemeKararTalepSorgu(request);
            log.info("Mahkeme karar talep sorgu successful for ID: {}", request.getId());
            return response;
        } catch (RestClientException e) {
            log.error("Mahkeme karar talep sorgu failed for ID {}: {}", request.getId(), e.getMessage(), e);
            throw new RuntimeException("Mahkeme karar talep sorgu failed", e);
        }
    }

    /**
     * İşlenecek karar listesi sorgulama işlemi
     *
     * @param request Sorgulama parametreleri
     * @return IDMahkemeKararTalepIslenecekResponse response
     */
    public IDMahkemeKararTalepIslenecekResponse islenecekKararListele(IDMahkemeKararTalepIslenecekRequest request) {
        try {
            log.info("Fetching islenecek karar listesi for request ID: {}", request.getId());
            IDMahkemeKararTalepIslenecekResponse response = mahkemeKararTalepControllerApi.islenecekKararListele(request);
            log.info("İşlenecek karar listesi fetch successful for ID: {}", request.getId());
            return response;
        } catch (RestClientException e) {
            log.error("İşlenecek karar listesi fetch failed for ID {}: {}", request.getId(), e.getMessage(), e);
            throw new RuntimeException("İşlenecek karar listesi fetch failed", e);
        }
    }

    /**
     * Mahkeme karar talep bilgisi sorgulama işlemi
     *
     * @param request Sorgulama parametreleri
     * @return MahkemeKararTalepQueryResponse response
     */
    public MahkemeKararTalepQueryResponse mahkemeKararTalepBilgisi(MahkemeKararTalepBilgisiRequest request) {
        try {
            log.info("Fetching mahkeme karar talep bilgisi for request");
            MahkemeKararTalepQueryResponse response = mahkemeKararTalepControllerApi.mahkemeKararTalepBilgisi(request);
            log.info("Mahkeme karar talep bilgisi fetch successful");
            return response;
        } catch (RestClientException e) {
            log.error("Mahkeme karar talep bilgisi fetch failed: {}", e.getMessage(), e);
            throw new RuntimeException("Mahkeme karar talep bilgisi fetch failed", e);
        }
    }

    /**
     * Sonlandırma kararı ID işlemi
     *
     * @param mahkemeKararDosyasi Mahkeme karar dosyası
     * @param mahkemeKararDetay   Mahkeme karar detayları
     * @return IDSonlandirmaKarariResponse response
     */
    public IDSonlandirmaKarariResponse sonlandirmaKarariID(File mahkemeKararDosyasi, IDSonlandirmaKarariRequest mahkemeKararDetay) {
        try {
            log.info("Sending sonlandirma karari ID for request ID: {}", mahkemeKararDetay.getId());
            IDSonlandirmaKarariResponse response = mahkemeKararTalepControllerApi.sonlandirmaKarariID(mahkemeKararDosyasi, mahkemeKararDetay);
            log.info("Sonlandirma karari ID send successful for ID: {}", mahkemeKararDetay.getId());
            return response;
        } catch (RestClientException e) {
            log.error("Sonlandirma karari ID send failed for ID {}: {}", mahkemeKararDetay.getId(), e.getMessage(), e);
            throw new RuntimeException("Sonlandirma karari ID send failed", e);
        }
    }

    /**
     * Suç tipi güncelleme işlemi
     *
     * @param mahkemeKararDosyasi Mahkeme karar dosyası
     * @param mahkemeKararDetay   Mahkeme karar detayları
     * @return IDSucTipiGuncellemeResponse response
     */
    public IDSucTipiGuncellemeResponse sucTipiGuncelle(File mahkemeKararDosyasi, IDSucTipiGuncellemeRequest mahkemeKararDetay) {
        try {
            log.info("Updating suc tipi for request ID: {}", mahkemeKararDetay.getId());
            IDSucTipiGuncellemeResponse response = mahkemeKararTalepControllerApi.sucTipiGuncelle(mahkemeKararDosyasi, mahkemeKararDetay);
            log.info("Suc tipi update successful for ID: {}", mahkemeKararDetay.getId());
            return response;
        } catch (RestClientException e) {
            log.error("Suc tipi update failed for ID {}: {}", mahkemeKararDetay.getId(), e.getMessage(), e);
            throw new RuntimeException("Suc tipi update failed", e);
        }
    }

    /**
     * Talep güncelleme işlemi
     *
     * @param request Talep güncelleme parametreleri
     * @return MahkemeKararTalepStateUpdateResponse response
     */
    public MahkemeKararTalepStateUpdateResponse talepGuncelle(MahkemeKararTalepStateUpdateRequest request) {
        try {
            log.info("Updating talep state for request");
            MahkemeKararTalepStateUpdateResponse response = mahkemeKararTalepControllerApi.talepGuncelle(request);
            log.info("Talep state update successful");
            return response;
        } catch (RestClientException e) {
            log.error("Talep state update failed: {}", e.getMessage(), e);
            throw new RuntimeException("Talep state update failed", e);
        }
    }

    /**
     * Uzatma kararı ID işlemi
     *
     * @param mahkemeKararDosyasi Mahkeme karar dosyası
     * @param mahkemeKararDetay   Mahkeme karar detayları
     * @return IDUzatmaKarariResponse response
     */
    public IDUzatmaKarariResponse uzatmaKarariID(File mahkemeKararDosyasi, IDUzatmaKarariRequest mahkemeKararDetay) {
        try {
            log.info("Sending uzatma karari ID for request ID: {}", mahkemeKararDetay.getId());
            IDUzatmaKarariResponse response = mahkemeKararTalepControllerApi.uzatmaKarariID(mahkemeKararDosyasi, mahkemeKararDetay);
            log.info("Uzatma karari ID send successful for ID: {}", mahkemeKararDetay.getId());
            return response;
        } catch (RestClientException e) {
            log.error("Uzatma karari ID send failed for ID {}: {}", mahkemeKararDetay.getId(), e.getMessage(), e);
            throw new RuntimeException("Uzatma karari ID send failed", e);
        }
    }
}

