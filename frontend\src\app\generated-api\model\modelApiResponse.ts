/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface ModelApiResponse { 
    responseCode?: ModelApiResponse.ResponseCodeEnum;
    responseMessage?: string;
}
export namespace ModelApiResponse {
    export const ResponseCodeEnum = {
        Success: 'SUCCESS',
        InvalidRequest: 'INVALID_REQUEST',
        Failed: 'FAILED'
    } as const;
    export type ResponseCodeEnum = typeof ResponseCodeEnum[keyof typeof ResponseCodeEnum];
}


