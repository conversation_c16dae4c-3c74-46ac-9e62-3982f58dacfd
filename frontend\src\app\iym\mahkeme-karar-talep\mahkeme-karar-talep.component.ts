import {Component, OnInit} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from '@angular/forms';
import {CardModule} from 'primeng/card';
import {ButtonModule} from 'primeng/button';
import {InputTextModule} from 'primeng/inputtext';

import {SelectModule} from 'primeng/select';
import {DatePickerModule} from 'primeng/datepicker';
import {CheckboxModule} from 'primeng/checkbox';
import {ToastModule} from 'primeng/toast';
import {ProgressSpinnerModule} from 'primeng/progressspinner';
import {FileUploadModule} from 'primeng/fileupload';
import {TabViewModule} from 'primeng/tabview';
import {MultiSelectModule} from 'primeng/multiselect';

import {TableModule} from 'primeng/table';
import {DialogModule} from 'primeng/dialog';
import {ChipModule} from 'primeng/chip';
import {MessageService} from 'primeng/api';
import {ErrorHandlingService} from '../shared/services/error-handling.service';
import {EvrakDetay, Hedef, IDHedefDetay, MahkemeKararBilgisi, MakosControllerService} from '../../generated-api';
import {KararTuruEnum, KararTuruOptions} from '../shared/enums/KararTuruEnum';
import {formatEnumLabel} from "../shared/utils/enumutil";
import HedefTipEnum = Hedef.HedefTipEnum;
import SureTipEnum = IDHedefDetay.SureTipEnum;
import EvrakTuruEnum = EvrakDetay.EvrakTuruEnum;
import MahkemeKararTipiEnum = MahkemeKararBilgisi.MahkemeKararTipiEnum;

@Component({
    selector: 'app-mahkeme-karar-talep',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        CardModule,
        ButtonModule,
        InputTextModule,
        SelectModule,
        DatePickerModule,
        CheckboxModule,
        ToastModule,
        ProgressSpinnerModule,
        FileUploadModule,
        TabViewModule,
        MultiSelectModule,
        TableModule,
        DialogModule,
        ChipModule
    ],
    providers: [],
    templateUrl: './mahkeme-karar-talep.component.html',
    styleUrls: ['./mahkeme-karar-talep.component.scss']
})
export class MahkemeKararTalepComponent implements OnInit {
    commonForm!: FormGroup;
    idYeniKararForm!: FormGroup;
    idUzatmaKarariForm!: FormGroup;
    idSonlandirmaKarariForm!: FormGroup;
    idHedefGuncellemeForm!: FormGroup;
    idMahkemeKararGuncellemeForm!: FormGroup;
    idAidiyatGuncellemeForm!: FormGroup;
    itKararForm!: FormGroup;

    // Enum'u template'te kullanabilmek için public olarak tanımlıyoruz (ENUM'u HTML'e açmak için:)
    KararTuruEnum = KararTuruEnum;
    kararTuru: KararTuruEnum | null = null;

    onKararTuruChanged(event: any): void {
        // this.kararTuru = KararTuruEnum[event.value as keyof typeof KararTuruEnum] // enum olarak saklıyoruz
    }

    loading = false;
    seciliDosya: File | null = null;
    activeTabIndex = 0;

    // Hedef dialog properties
    hedefDialogVisible = false;
    hedefForm!: FormGroup;
    editingHedefIndex = -1;

    // Dropdown options and loading states
    iller: any[] = [];
    kurumlar: any[] = [];
    mahkemeKodlari: any[] = [];
    sucTipleri: any[] = [];

    dropdownLoading = {
        iller: false,
        kurumlar: false,
        mahkemeKodlari: false,
        sucTipleri: false,
        mahkemeKararTipleri: false,
        tespitTurleri: false
    };

    // Form arrays for hedef detayları
    get hedefDetayListesi(): FormArray {
        return this.idYeniKararForm.get('hedefDetayListesi') as FormArray;
    }

    get hedefler(): any[] {
        return this.hedefDetayListesi?.value || [];
    }

    // Aidiyat kodları için
    newAidiyatKodu: string = '';
    aidiyatKodlariOptions: any[] = [];

    // Hedef aidiyat kodları için
    newHedefAidiyatKodu: string = '';
    hedefAidiyatKodlariOptions: any[] = [];

    kararTuruOptions: { label: string; value: string }[] = [];

    // Enum değerlerinden dinamik olarak oluşturulan seçenek listeleri
    evrakTuruOptions: { label: string; value: string }[] = [];
    mahkemeKararTipOptions: { label: string; value: string }[] = [];
    hedefTipOptions: { label: string; value: string }[] = [];
    sureTipiOptions: { label: string; value: string }[] = [];

    constructor(
        private fb: FormBuilder,
        private makosService: MakosControllerService,
        private messageService: MessageService,
        private errorHandlingService: ErrorHandlingService
    ) {
    }

    ngOnInit(): void {
        // Enum değerlerinden dinamik olarak seçenek listelerini oluştur
        this.initializeEnumOptions();

        // Formları oluştur
        this.createForms();

        // API'den gelen verileri yükle
        this.loadDropdownData();
    }

    private initializeEnumOptions(): void {
        // KararTuruEnum değerlerinden seçenek listesi oluştur
        this.kararTuruOptions = KararTuruOptions;

        // EvrakTuruEnum değerlerinden seçenek listesi oluştur
        this.evrakTuruOptions = Object.entries(EvrakTuruEnum).map(([key, value]) => ({
            label: formatEnumLabel(key),
            value: value
        }));

        // HedefTipEnum değerlerinden seçenek listesi oluştur
        this.hedefTipOptions = Object.entries(HedefTipEnum).map(([key, value]) => ({
            label: formatEnumLabel(key),
            value: value
        }));

        // SureTipEnum değerlerinden seçenek listesi oluştur
        this.sureTipiOptions = Object.entries(SureTipEnum).map(([key, value]) => ({
            label: formatEnumLabel(key),
            value: value
        }));

        // MahkemeKararTipiEnum değerlerinden seçenek listesi oluştur
        this.mahkemeKararTipOptions = Object.entries(MahkemeKararTipiEnum).map(([key, value]) => ({
            label: formatEnumLabel(key),
            value: value
        }));
    }

    private loadDropdownData(): void {
        // Load dropdown data for ID Yeni Karar
        this.loadIller();
        this.loadKurumlar();
        this.loadMahkemeKodlari();
        this.loadSucTipleri();
        // this.loadMahkemeKararTipleri(); // Artık enum değerlerinden oluşturulduğu için API çağrısına gerek yok
        // this.loadTespitTurleri();
    }

    private loadIller(): void {
        this.dropdownLoading.iller = true;
        this.makosService.iller().subscribe({
            next: (response) => {
                if (response.success && response.result?.iller) {
                    this.iller = response.result.iller.map(item => ({
                        label: `${item.ilAdi || ''} ${item.ilceAdi || ''}`.trim(),
                        value: item.ilKod || ''
                    }));
                } else {
                    this.messageService.add({
                        severity: 'error',
                        summary: 'Hata',
                        detail: 'İller yüklenemedi.'
                    });
                }
                this.dropdownLoading.iller = false;
            },
            error: (error) => {
                console.error('İller yükleme hatası:', error);
                this.messageService.add({
                    severity: 'error',
                    summary: 'Hata',
                    detail: 'İller yüklenirken bir hata oluştu.'
                });
                this.dropdownLoading.iller = false;
            }
        });
    }

    private loadKurumlar(): void {
        this.dropdownLoading.kurumlar = true;
        this.makosService.kurumlar().subscribe({
            next: (response) => {
                if (response.success && response.result?.kurumlar) {
                    this.kurumlar = response.result.kurumlar.map(item => ({
                        label: item.kurumAdi || '',
                        value: item.kurumKod || ''
                    }));
                } else {
                    this.messageService.add({
                        severity: 'error',
                        summary: 'Hata',
                        detail: 'Kurumlar yüklenemedi.'
                    });
                }
                this.dropdownLoading.kurumlar = false;
            },
            error: (error) => {
                this.errorHandlingService.handleDropdownError('Kurumlar', () => {
                    this.dropdownLoading.kurumlar = false;
                });
            }
        });
    }

    private loadMahkemeKodlari(): void {
        this.dropdownLoading.mahkemeKodlari = true;
        this.makosService.mahkemeKodlari().subscribe({
            next: (response) => {
                if (response.success && response.result?.mahkemeKodListesi) {
                    this.mahkemeKodlari = response.result.mahkemeKodListesi.map(item => ({
                        label: item.mahkemeAdi || '',
                        value: item.mahkemeKodu || ''
                    }));
                } else {
                    this.messageService.add({
                        severity: 'error',
                        summary: 'Hata',
                        detail: 'Mahkeme kodları yüklenemedi.'
                    });
                }
                this.dropdownLoading.mahkemeKodlari = false;
            },
            error: (error) => {
                this.errorHandlingService.handleDropdownError('Mahkeme kodları', () => {
                    this.dropdownLoading.mahkemeKodlari = false;
                });
            }
        });
    }

    private loadSucTipleri(): void {
        this.dropdownLoading.sucTipleri = true;
        this.makosService.sucTipleri().subscribe({
            next: (response) => {
                if (response.success && response.result?.sucTipleri) {
                    this.sucTipleri = response.result.sucTipleri.map(item => ({
                        label: item.aciklama || '',
                        value: item.sucTipiKodu || ''
                    }));
                } else {
                    this.messageService.add({
                        severity: 'error',
                        summary: 'Hata',
                        detail: 'Suç tipleri yüklenemedi.'
                    });
                }
                this.dropdownLoading.sucTipleri = false;
            },
            error: (error) => {
                this.errorHandlingService.handleDropdownError('Suç tipleri', () => {
                    this.dropdownLoading.sucTipleri = false;
                });
            }
        });
    }

    private loadMahkemeKararTipleri(): void {
        this.dropdownLoading.mahkemeKararTipleri = true;

        // Enum değerlerinden dinamik olarak seçenek listesi oluştur
        this.mahkemeKararTipOptions = Object.entries(MahkemeKararTipiEnum).map(([key, value]) => ({
            label: formatEnumLabel(key),
            value: value
        }));

        this.dropdownLoading.mahkemeKararTipleri = false;
    }


    // private loadTespitTurleri(): void {
    //     this.dropdownLoading.tespitTurleri = true;
    //     this.makosService.tespitTurleri().subscribe({
    //         next: (response) => {
    //             if (response.success && response.result?.tespitTurleri) {
    //                 this.hedefTipOptions = response.result.tespitTurleri.map(item => ({
    //                     label: item.aciklama || '',
    //                     value: item.tespitTuru?.toString() || ''
    //                 }));
    //             } else {
    //                 this.messageService.add({
    //                     severity: 'error',
    //                     summary: 'Hata',
    //                     detail: 'Tespit türleri yüklenemedi.'
    //                 });
    //             }
    //             this.dropdownLoading.tespitTurleri = false;
    //         },
    //         error: (error) => {
    //             this.errorHandlingService.handleDropdownError('Tespit türleri', () => {
    //                 this.dropdownLoading.tespitTurleri = false;
    //             });
    //         }
    //     });
    // }

    addNewAidiyatKodu(): void {
        if (this.newAidiyatKodu && this.newAidiyatKodu.trim()) {
            const trimmedKod = this.newAidiyatKodu.trim();

            // Check if the code already exists in options
            const exists = this.aidiyatKodlariOptions.some(option => option.value === trimmedKod);

            if (!exists) {
                // Create new array with new option to ensure proper change detection
                const newOptions = [...this.aidiyatKodlariOptions, {
                    label: trimmedKod,
                    value: trimmedKod
                }];
                this.aidiyatKodlariOptions = newOptions;

                // Add to form control value - ensure we're working with a new array
                const currentValues = [...(this.idYeniKararForm.get('mahkemeAidiyatKodlari')?.value || [])];
                if (!currentValues.includes(trimmedKod)) {
                    currentValues.push(trimmedKod);
                    this.idYeniKararForm.get('mahkemeAidiyatKodlari')?.setValue(currentValues);
                }

                this.messageService.add({
                    severity: 'success',
                    summary: 'Başarılı',
                    detail: `Aidiyat kodu "${trimmedKod}" eklendi.`
                });
            } else {
                this.messageService.add({
                    severity: 'warn',
                    summary: 'Uyarı',
                    detail: 'Bu aidiyat kodu zaten mevcut.'
                });
            }

            // Clear input
            this.newAidiyatKodu = '';
        }
    }

    addNewHedefAidiyatKodu(): void {
        if (this.newHedefAidiyatKodu && this.newHedefAidiyatKodu.trim()) {
            const trimmedKod = this.newHedefAidiyatKodu.trim();

            // Check if the code already exists in options
            const exists = this.hedefAidiyatKodlariOptions.some(option => option.value === trimmedKod);

            if (!exists) {
                // Create new array with new option to ensure proper change detection
                const newOptions = [...this.hedefAidiyatKodlariOptions, {
                    label: trimmedKod,
                    value: trimmedKod
                }];
                this.hedefAidiyatKodlariOptions = newOptions;

                // Add to form control value - ensure we're working with a new array
                const currentValues = [...(this.hedefForm.get('hedefAidiyatKodlari')?.value || [])];
                if (!currentValues.includes(trimmedKod)) {
                    currentValues.push(trimmedKod);
                    this.hedefForm.get('hedefAidiyatKodlari')?.setValue(currentValues);
                }

                this.messageService.add({
                    severity: 'success',
                    summary: 'Başarılı',
                    detail: `Hedef aidiyat kodu "${trimmedKod}" eklendi.`
                });
            } else {
                this.messageService.add({
                    severity: 'warn',
                    summary: 'Uyarı',
                    detail: 'Bu hedef aidiyat kodu zaten mevcut.'
                });
            }

            // Clear input
            this.newHedefAidiyatKodu = '';
        }
    }

    private createForms(): void {
        this.commonForm = this.fb.group({
            kararTuru: [this.kararTuru, Validators.required],
            evrakNo: ['', Validators.required],
            evrakTarihi: [null, Validators.required],
            evrakKurumKodu: ['', Validators.required],
            evrakTuru: ['', Validators.required],
            geldigiIlIlceKodu: ['', Validators.required],
            havaleBirimi: ['', [Validators.maxLength(10)]],
            evrakKonusu: [''],
            evrakAciklama: [''],
            acilmi: [false],
            mahkemeKararTipi: ['', Validators.required],
            mahkemeKodu: ['', Validators.required],
            mahkemeKararNo: ['', Validators.required],
            mahkemeIlIlceKodu: ['', Validators.required],
            sorusturmaNo: [''],
            mahkemeAciklama: ['']
        });

        this.idYeniKararForm = this.fb.group({
            hedefDetayListesi: this.fb.array([]),
            mahkemeAidiyatKodlari: [[]],
            mahkemeSucTipiKodlari: [[]]
        });

        this.idUzatmaKarariForm = this.fb.group({
            hedefDetayListesi: [[]]
        });

        this.idSonlandirmaKarariForm = this.fb.group({
            hedefDetayListesi: [[]]
        });

        this.idHedefGuncellemeForm = this.fb.group({
            hedefDetayListesi: [[]]
        });

        this.idMahkemeKararGuncellemeForm = this.fb.group({
            mahkemeAidiyatKodlari: [[]],
            mahkemeSucTipiKodlari: [[]]
        });

        this.idAidiyatGuncellemeForm = this.fb.group({
            mahkemeAidiyatKodlari: [[]],
            mahkemeSucTipiKodlari: [[]]
        });

        this.itKararForm = this.fb.group({
            hedefDetayListesi: [[]]
        });

        // Hedef form for dialog
        this.hedefForm = this.fb.group({
            hedefNo: ['', Validators.required],
            hedefTip: [HedefTipEnum.Gsm, Validators.required],
            hedefAd: ['', Validators.required],
            hedefSoyad: ['', Validators.required],
            baslamaTarihi: [new Date(), Validators.required],
            sure: ['30'],
            sureTip: [SureTipEnum.Gun],
            hedefAidiyatKodlari: [[], Validators.required],
            canakNo: ['']
        });

        this.commonForm.get('kararTuru')?.valueChanges.subscribe(value => {
            this.kararTuru = value;
            this.activeTabIndex = 0;
        });
    }

    /**
     * Form verilerinden request nesnesini hazırlar
     * @returns Request nesnesi (IDYeniKararRequest, ITKararRequest veya IDMahkemeKararGuncellemeRequest için temel nesne)
     */
    private prepareRequestData(): any {
        const commonData = this.commonForm.value;

        return {
            id: crypto.randomUUID(), // Benzersiz ID ekliyoruz
            kararTuru: commonData.kararTuru, // Enum değerleri doğrudan backend ile uyumlu
            evrakDetay: {
                evrakNo: commonData.evrakNo,
                evrakTarihi: commonData.evrakTarihi,
                evrakKurumKodu: commonData.evrakKurumKodu,
                evrakTuru: commonData.evrakTuru,
                geldigiIlIlceKodu: commonData.geldigiIlIlceKodu,
                havaleBirimi: commonData.havaleBirimi,
                evrakKonusu: commonData.evrakKonusu,
                evrakAciklama: commonData.evrakAciklama,
                acilmi: commonData.acilmi
            },
            mahkemeKararBilgisi: {
                mahkemeKararTipi: commonData.mahkemeKararTipi,
                mahkemeKararDetay: {
                    mahkemeKodu: commonData.mahkemeKodu,
                    mahkemeKararNo: commonData.mahkemeKararNo,
                    mahkemeIlIlceKodu: commonData.mahkemeIlIlceKodu,
                    sorusturmaNo: commonData.sorusturmaNo,
                    aciklama: commonData.mahkemeAciklama
                }
            }
        };
    }

    async onSubmit(): Promise<void> {
        if (this.commonForm.invalid) {
            Object.keys(this.commonForm.controls).forEach(key => {
                this.commonForm.get(key)?.markAsTouched();
            });
            this.messageService.add({
                severity: 'error',
                summary: 'Hata',
                detail: 'Lütfen tüm zorunlu alanları doldurun.'
            });
            return;
        }

        this.loading = true;
        const file = this.seciliDosya || new Blob();

        // Karar türüne göre özel alanları içeren tam request nesnesini hazırla
        const requestData = this.prepareFullRequestData();

        switch (requestData.kararTuru) {
            case KararTuruEnum.IletisiminDenetlenmesiYeniKarar:
                // ID_YENI_KARAR için doğrudan JSON nesnesi gönderiyoruz
                this.makosService.yeniKararID(file, requestData).subscribe({
                    next: (response: any) => this.handleSuccess(response),
                    error: (error: any) => this.handleError(error)
                });
                break;
            case KararTuruEnum.IletisiminTespiti:
                // IT_KARAR için doğrudan JSON nesnesi gönderiyoruz
                this.makosService.yeniKararIT(file, requestData).subscribe({
                    next: (response: any) => this.handleSuccess(response),
                    error: (error: any) => this.handleError(error)
                });
                break;
            case KararTuruEnum.IletisiminDenetlenmesiUzatmaKarari:
            case KararTuruEnum.IletisiminDenetlenmesiSonlandirmaKarari:
            case KararTuruEnum.IletisiminDenetlenmesiHedefGuncelleme:
            case KararTuruEnum.IletisiminDenetlenmesiMahkemekararGuncelleme:
            case KararTuruEnum.IletisiminDenetlenmesiAidiyatGuncelleme:
                // Diğer durumlar için doğrudan JSON nesnesi gönderiyoruz
                this.makosService.mahkemeBilgisiGuncelle(requestData, file).subscribe({
                    next: (response: any) => this.handleSuccess(response),
                    error: (error: any) => this.handleError(error)
                });
                break;
        }
    }

    /**
     * Form verilerini hazırlar ve FormData nesnesini döndürür
     * @returns FormData nesnesi
     */
    private prepareFormData(): FormData {
        const formData = new FormData();
        const request = this.prepareFullRequestData();

        formData.append('mahkemeKararDetay', new Blob([JSON.stringify(request)], {type: 'application/json'}));

        if (this.seciliDosya) {
            formData.append('mahkemeKararDosyasi', this.seciliDosya);
        }

        return formData;
    }

    /**
     * Karar türüne göre özel alanları içeren tam request nesnesini hazırlar
     * @returns Tam request nesnesi
     */
    private prepareFullRequestData(): any {
        // Temel request nesnesini oluştur
        const request = this.prepareRequestData();

        // Karar türüne göre özel alanları ekle
        switch (request.kararTuru) {
            case KararTuruEnum.IletisiminDenetlenmesiYeniKarar:
                // ID Yeni Karar için hedef detayları ve aidiyat kodlarını ekle
                request.hedefDetayListesi = this.hedefDetayListesi.value.map((hedef: any) => this.transformHedefToBackendFormat(hedef)) || [];
                request.mahkemeAidiyatKodlari = this.idYeniKararForm.get('mahkemeAidiyatKodlari')?.value || [];
                request.mahkemeSucTipiKodlari = this.idYeniKararForm.get('mahkemeSucTipiKodlari')?.value || [];
                break;
            case KararTuruEnum.IletisiminDenetlenmesiUzatmaKarari:
                request.hedefDetayListesi = this.idUzatmaKarariForm.get('hedefDetayListesi')?.value || [];
                break;
            case KararTuruEnum.IletisiminDenetlenmesiSonlandirmaKarari:
                request.hedefDetayListesi = this.idSonlandirmaKarariForm.get('hedefDetayListesi')?.value || [];
                break;
            case KararTuruEnum.IletisiminDenetlenmesiHedefGuncelleme:
                request.hedefDetayListesi = this.idHedefGuncellemeForm.get('hedefDetayListesi')?.value || [];
                break;
            case KararTuruEnum.IletisiminDenetlenmesiMahkemekararGuncelleme:
                request.mahkemeAidiyatKodlari = this.idMahkemeKararGuncellemeForm.get('mahkemeAidiyatKodlari')?.value || [];
                request.mahkemeSucTipiKodlari = this.idMahkemeKararGuncellemeForm.get('mahkemeSucTipiKodlari')?.value || [];
                break;
            case KararTuruEnum.IletisiminDenetlenmesiAidiyatGuncelleme:
                request.mahkemeAidiyatKodlari = this.idAidiyatGuncellemeForm.get('mahkemeAidiyatKodlari')?.value || [];
                request.mahkemeSucTipiKodlari = this.idAidiyatGuncellemeForm.get('mahkemeSucTipiKodlari')?.value || [];
                break;
            case KararTuruEnum.IletisiminTespiti:
                request.hedefDetayListesi = this.itKararForm.get('hedefDetayListesi')?.value || [];
                break;
        }

        return request;
    }

    private transformHedefToBackendFormat(hedef: any): any {
        return {
            hedefNoAdSoyad: {
                hedef: {
                    hedefNo: hedef.hedefNo,
                    hedefTip: hedef.hedefTip
                },
                hedefAd: hedef.hedefAd,
                hedefSoyad: hedef.hedefSoyad
            },
            baslamaTarihi: hedef.baslamaTarihi,
            sure: hedef.sure,
            sureTip: hedef.sureTip,
            hedefAidiyatKodlari: hedef.hedefAidiyatKodlari,
            canakNo: hedef.canakNo,
            uzatmaSayisi: hedef.uzatmaSayisi,
            ilgiliMahkemeKararDetayi: hedef.ilgiliMahkemeKararDetayi
        };
    }


    private handleSuccess(response: unknown): void {
        this.loading = false;
        this.messageService.add({
            severity: 'success',
            summary: 'Başarılı',
            detail: 'Mahkeme karar talebi başarıyla gönderildi.'
        });
        this.clearForm();
    }

    private handleError(err: any): void {
        this.errorHandlingService.handleError(err, () => {
            this.loading = false;
        });
    }


    editHedef(hedef: any, index: number): void {
        // In a real implementation, this would open an edit dialog
        console.log('Edit hedef:', hedef, 'at index:', index);
    }

    clearForm(): void {
        this.commonForm.reset();
        this.idYeniKararForm.reset();
        this.idUzatmaKarariForm.reset();
        this.idSonlandirmaKarariForm.reset();
        this.idHedefGuncellemeForm.reset();
        this.idMahkemeKararGuncellemeForm.reset();
        this.idAidiyatGuncellemeForm.reset();
        this.itKararForm.reset();

        // Clear hedef data
        this.hedefDetayListesi.clear();

        // Reset file selection
        this.seciliDosya = null;

        // Reset aidiyat kodları options
        this.aidiyatKodlariOptions = [];
        this.newAidiyatKodu = '';

        // Reset to default karar türü
        this.kararTuru = KararTuruEnum.IletisiminDenetlenmesiYeniKarar;
        this.commonForm.get('kararTuru')?.setValue(this.kararTuru);
        this.activeTabIndex = 0;

        this.messageService.add({
            severity: 'info',
            summary: 'Temizlendi',
            detail: 'Form başarıyla temizlendi.'
        });
    }

    fillTestDataForSelectedType(): void {
        switch (this.kararTuru) {
            case KararTuruEnum.IletisiminDenetlenmesiYeniKarar:
                this.fillIDYeniKararTestData();
                break;
            case KararTuruEnum.IletisiminDenetlenmesiUzatmaKarari:
                this.fillIDUzatmaKarariTestData();
                break;
            case KararTuruEnum.IletisiminDenetlenmesiSonlandirmaKarari:
                this.fillIDSonlandirmaKarariTestData();
                break;
            case KararTuruEnum.IletisiminDenetlenmesiHedefGuncelleme:
                this.fillIDHedefGuncellemeTestData();
                break;
            case KararTuruEnum.IletisiminDenetlenmesiMahkemekararGuncelleme:
                this.fillIDMahkemeKararGuncellemeTestData();
                break;
            case KararTuruEnum.IletisiminDenetlenmesiAidiyatGuncelleme:
                this.fillIDAidiyatGuncellemeTestData();
                break;
            case KararTuruEnum.IletisiminTespiti:
                this.fillITKararTestData();
                break;
            default:
                this.fillCommonTestData();
                break;
        }
    }

    fillCommonTestData(): void {
        // Get valid values from loaded dropdown options
        const validIlKodu = this.iller.length > 0 ? this.iller[0].value : '3400'; // Default to Istanbul if available
        const validMahkemeKodu = this.mahkemeKodlari.length > 0 ? this.mahkemeKodlari[0].value : '08030100';

        this.commonForm.patchValue({
            evrakNo: '2024-TEST-001',
            evrakTarihi: new Date(),
            evrakKurumKodu: '02', // EGMIDB kurumu
            evrakTuru: EvrakTuruEnum.IletisiminDenetlenmesi,
            geldigiIlIlceKodu: validIlKodu,
            havaleBirimi: '02',//todo 02 egm, 03 mit, 04 jandarma
            evrakKonusu: 'İletişimin Denetlenmesi Test Talebi',
            evrakAciklama: 'Test amaçlı mahkeme karar talebi - Otomatik doldurulmuş',
            acilmi: true,
            mahkemeKararTipi: MahkemeKararTipiEnum.AdliHakimKarari, // Doğrudan enum değeri kullanıyoruz
            mahkemeKodu: validMahkemeKodu,
            mahkemeKararNo: '2024/TEST-123',
            mahkemeIlIlceKodu: validIlKodu,
            sorusturmaNo: '2024-SOR-001',
            mahkemeAciklama: 'Test mahkeme kararı açıklaması'
        });

        // Create and set test PDF file
        this.createTestFile('test-mahkeme-karar.pdf');

        this.messageService.add({
            severity: 'info',
            summary: 'Test Verisi',
            detail: 'Ortak alanlara test verisi yüklendi ve test PDF dosyası oluşturuldu.'
        });
    }

    fillIDYeniKararTestData(): void {
        this.fillCommonTestData();

        // Get valid values from loaded dropdown options
        const validSucTipleri = this.sucTipleri.length > 0 ? [this.sucTipleri[0].value, this.sucTipleri[1]?.value].filter(Boolean) : ['1312', '1313'];

        // Fill form with valid select values
        this.idYeniKararForm.patchValue({
            mahkemeAidiyatKodlari: ['********', '********'], // EGMIDB için geçerli aidiyat kodları
            mahkemeSucTipiKodlari: validSucTipleri
        });

        // Add test aidiyat kodları to options if not already present
        const testAidiyatKodlari = ['********', '********'];
        const newAidiyatOptions = [...this.aidiyatKodlariOptions];
        testAidiyatKodlari.forEach(kod => {
            const exists = newAidiyatOptions.some(option => option.value === kod);
            if (!exists) {
                newAidiyatOptions.push({
                    label: kod,
                    value: kod
                });
            }
        });
        this.aidiyatKodlariOptions = newAidiyatOptions;

        // Clear existing hedef data and add test hedefler
        this.hedefDetayListesi.clear();
        this.addTestHedef('Mehmet', 'Test', '1');
        this.addTestHedef('Ahmet', 'Örnek', '2');

        this.createTestFile('id_yeni_karar_test.pdf');
        this.messageService.add({
            severity: 'info',
            summary: 'Test Verisi',
            detail: 'ID Yeni Karar için test verisi yüklendi ve hedefler eklendi.'
        });
    }

    fillIDUzatmaKarariTestData(): void {
        this.fillCommonTestData();
        this.idUzatmaKarariForm.patchValue({
            hedefDetayListesi: 'Hedef 1: 5551234567 (GSM)\nHedef 2: 5559876543 (GSM)\nTest amaçlı uzatma kararı hedef detayları'
        });
        this.createTestFile('id_uzatma_karari_test.pdf');
        this.messageService.add({
            severity: 'info',
            summary: 'Test Verisi',
            detail: 'ID Uzatma Kararı için test verisi yüklendi.'
        });
    }

    fillIDSonlandirmaKarariTestData(): void {
        this.fillCommonTestData();
        this.idSonlandirmaKarariForm.patchValue({
            hedefDetayListesi: 'Hedef 1: 5551234567 (GSM) - Sonlandırılacak\nHedef 2: 5559876543 (GSM) - Sonlandırılacak\nTest amaçlı sonlandırma kararı hedef detayları'
        });
        this.createTestFile('id_sonlandirma_karari_test.pdf');
        this.messageService.add({
            severity: 'info',
            summary: 'Test Verisi',
            detail: 'ID Sonlandırma Kararı için test verisi yüklendi.'
        });
    }

    fillIDHedefGuncellemeTestData(): void {
        this.fillCommonTestData();
        this.idHedefGuncellemeForm.patchValue({
            hedefDetayListesi: 'Hedef 1: 5551112233 (GSM) - Güncellenecek\nHedef 2: 5554445566 (GSM) - Güncellenecek\nTest amaçlı hedef güncelleme detayları'
        });
        this.createTestFile('id_hedef_guncelleme_test.pdf');
        this.messageService.add({
            severity: 'info',
            summary: 'Test Verisi',
            detail: 'ID Hedef Güncelleme için test verisi yüklendi.'
        });
    }

    fillIDMahkemeKararGuncellemeTestData(): void {
        this.fillCommonTestData();

        // Get valid values from loaded dropdown options
        const validSucTipleri = this.sucTipleri.length > 0 ? [this.sucTipleri[0].value, this.sucTipleri[1]?.value].filter(Boolean) : ['1312', '1313'];

        this.idMahkemeKararGuncellemeForm.patchValue({
            mahkemeAidiyatKodlari: ['********', '********'], // EGMIDB için geçerli aidiyat kodları
            mahkemeSucTipiKodlari: validSucTipleri
        });

        // Add test aidiyat kodları to options if not already present
        const testAidiyatKodlari = ['********', '********'];
        const newAidiyatOptions = [...this.aidiyatKodlariOptions];
        testAidiyatKodlari.forEach(kod => {
            const exists = newAidiyatOptions.some(option => option.value === kod);
            if (!exists) {
                newAidiyatOptions.push({
                    label: kod,
                    value: kod
                });
            }
        });
        this.aidiyatKodlariOptions = newAidiyatOptions;

        this.createTestFile('id_mahkeme_karar_guncelleme_test.pdf');
        this.messageService.add({
            severity: 'info',
            summary: 'Test Verisi',
            detail: 'ID Mahkeme Karar Güncelleme için test verisi yüklendi.'
        });
    }

    fillIDAidiyatGuncellemeTestData(): void {
        this.fillCommonTestData();

        // Get valid values from loaded dropdown options
        const validSucTipleri = this.sucTipleri.length > 0 ? [this.sucTipleri[0].value, this.sucTipleri[1]?.value].filter(Boolean) : ['1312', '1313'];

        this.idAidiyatGuncellemeForm.patchValue({
            mahkemeAidiyatKodlari: ['********', '********'], // EGMIDB için geçerli aidiyat kodları
            mahkemeSucTipiKodlari: validSucTipleri
        });

        // Add test aidiyat kodları to options if not already present
        const testAidiyatKodlari = ['********', '********'];
        const newAidiyatOptions = [...this.aidiyatKodlariOptions];
        testAidiyatKodlari.forEach(kod => {
            const exists = newAidiyatOptions.some(option => option.value === kod);
            if (!exists) {
                newAidiyatOptions.push({
                    label: kod,
                    value: kod
                });
            }
        });
        this.aidiyatKodlariOptions = newAidiyatOptions;

        this.createTestFile('id_aidiyat_guncelleme_test.pdf');
        this.messageService.add({
            severity: 'info',
            summary: 'Test Verisi',
            detail: 'ID Aidiyat Güncelleme için test verisi yüklendi.'
        });
    }

    fillITKararTestData(): void {
        this.fillCommonTestData();
        // IT Karar için evrakTuru ve mahkemeKararTipi değerlerini güncelle
        this.commonForm.patchValue({
            evrakTuru: EvrakTuruEnum.IletisiminTespiti,
            mahkemeKararTipi: MahkemeKararTipiEnum.OnleyiciHakimKarari // IT Karar için uygun mahkeme karar tipi
        });
        this.itKararForm.patchValue({
            hedefDetayListesi: 'IP Adresi 1: *************\nIP Adresi 2: ********\nDomain: example.com\nTest amaçlı IT karar hedef detayları'
        });
        this.createTestFile('it_karar_test.pdf');
        this.messageService.add({
            severity: 'info',
            summary: 'Test Verisi',
            detail: 'IT Karar için test verisi yüklendi.'
        });
    }

    // Hedef dialog methods
    hedefEkleDialog(): void {
        this.editingHedefIndex = -1;
        this.hedefForm.reset();
        this.hedefForm.patchValue({
            baslamaTarihi: new Date(),
            hedefTip: HedefTipEnum.Gsm,
            sureTip: SureTipEnum.Gun
        });
        this.hedefDialogVisible = true;
    }

    hedefDuzenleDialog(index: number): void {
        this.editingHedefIndex = index;
        const hedef = this.hedefDetayListesi.at(index).value;

        // Handle hedefAidiyatKodlari as array or string
        let hedefAidiyatKodlari: string[] = [];
        if (Array.isArray(hedef.hedefAidiyatKodlari)) {
            hedefAidiyatKodlari = [...hedef.hedefAidiyatKodlari];
        } else if (typeof hedef.hedefAidiyatKodlari === 'string') {
            hedefAidiyatKodlari = hedef.hedefAidiyatKodlari.split(',').map((k: string) => k.trim()).filter((k: string) => k);
        }

        this.hedefForm.patchValue({
            ...hedef,
            hedefTip: hedef.hedefTip,
            baslamaTarihi: new Date(hedef.baslamaTarihi),
            hedefAidiyatKodlari: hedefAidiyatKodlari
        });
        this.hedefDialogVisible = true;
    }

    hedefKaydet(): void {
        if (this.hedefForm.valid) {
            const hedefData = this.hedefForm.value;

            // Ensure hedefAidiyatKodlari is an array
            const hedefAidiyatKodlari = Array.isArray(hedefData.hedefAidiyatKodlari)
                ? hedefData.hedefAidiyatKodlari
                : [];

            const hedef = {
                ...hedefData,
                hedefAidiyatKodlari: hedefAidiyatKodlari
            };

            if (this.editingHedefIndex >= 0) {
                // Update existing hedef
                this.hedefDetayListesi.at(this.editingHedefIndex).patchValue(hedef);
                this.messageService.add({
                    severity: 'success',
                    summary: 'Başarılı',
                    detail: 'Hedef güncellendi',
                    life: 3000
                });
            } else {
                // Add new hedef
                const hedefForm = this.fb.group({
                    hedefNo: [hedef.hedefNo, Validators.required],
                    hedefTip: [hedef.hedefTip, Validators.required],
                    hedefAd: [hedef.hedefAd, Validators.required],
                    hedefSoyad: [hedef.hedefSoyad, Validators.required],
                    baslamaTarihi: [hedef.baslamaTarihi, Validators.required],
                    sure: [hedef.sure],
                    sureTip: [hedef.sureTip],
                    hedefAidiyatKodlari: [hedef.hedefAidiyatKodlari, Validators.required],
                    canakNo: [hedef.canakNo]
                });
                this.hedefDetayListesi.push(hedefForm);
                this.messageService.add({
                    severity: 'success',
                    summary: 'Başarılı',
                    detail: 'Hedef eklendi',
                    life: 3000
                });
            }

            this.hedefDialogVisible = false;
            this.hedefForm.reset();
        } else {
            this.messageService.add({
                severity: 'warn',
                summary: 'Uyarı',
                detail: 'Lütfen tüm zorunlu alanları doldurun'
            });
        }
    }

    hedefSil(index: number): void {
        this.hedefDetayListesi.removeAt(index);
        this.messageService.add({
            severity: 'success',
            summary: 'Başarılı',
            detail: 'Hedef silindi',
            life: 3000
        });
    }

    hedefDialogKapat(): void {
        this.hedefDialogVisible = false;
        this.hedefForm.reset();
        this.editingHedefIndex = -1;
    }

    getSureTipiLabel(value: SureTipEnum): string {
        const option = this.sureTipiOptions.find(opt => opt.value === value);
        return option ? option.label : value;
    }

    formatTarih(tarih: Date | string): string {
        if (!tarih) return '';
        const date = new Date(tarih);
        return date.toLocaleDateString('tr-TR');
    }

    getHedefTipLabel(value: HedefTipEnum): string {
        const option = this.hedefTipOptions.find(opt => opt.value === value);
        return option ? option.label : value;
    }

    // Form validation helpers for hedef form
    isHedefFieldInvalid(fieldName: string): boolean {
        const field = this.hedefForm.get(fieldName);
        return !!(field && field.invalid && (field.dirty || field.touched));
    }

    getHedefFieldError(fieldName: string): string {
        const field = this.hedefForm.get(fieldName);
        if (field && field.errors) {
            if (field.errors['required']) return 'Bu alan zorunludur';
        }
        return '';
    }

    // getHedefAidiyatKodlariArray(aidiyatKodlari: any): string[] {
    //   if (!aidiyatKodlari) {
    //     return [];
    //   }

    //   if (Array.isArray(aidiyatKodlari)) {
    //     return aidiyatKodlari.filter((kod: string) => kod && kod.trim().length > 0);
    //   }

    //   if (typeof aidiyatKodlari === 'string') {
    //     return aidiyatKodlari
    //       .split(',')
    //       .map((kod: string) => kod.trim())
    //       .filter((kod: string) => kod.length > 0 && kod !== 'null');
    //   }

    //   return [];
    // }

    getHedefAidiyatKodlariArray(kodlar: string | string[]): string[] {
        if (!kodlar) return [];
        if (Array.isArray(kodlar)) return kodlar;
        return kodlar.split(',').map(k => k.trim()).filter(k => k);
    }

    getHedefAidiyatKodlariDisplay(kodlar: string | string[]): string {
        const array = this.getHedefAidiyatKodlariArray(kodlar);
        if (array.length === 0) return '';
        if (array.length === 1) return array[0];
        if (array.length === 2) return array.join(', ');
        return `${array[0]}, ${array[1]}... (+${array.length - 2} daha)`;
    }

    onFileSelect(event: any): void {
        this.seciliDosya = event.files && event.files.length > 0 ? event.files[0] : null;
    }

    onFileRemove(event?: any): void {
        this.seciliDosya = null;
    }

    // Utility methods
    isFieldInvalid(fieldName: string, formGroup: FormGroup = this.commonForm): boolean {
        if (!formGroup) return false;
        const field = formGroup.get(fieldName);
        return !!(field && field.invalid && (field.dirty || field.touched));
    }

    getFieldError(fieldName: string, formGroup: FormGroup = this.commonForm): string {
        if (!formGroup) return '';
        const field = formGroup.get(fieldName);
        if (field && field.errors) {
            if (field.errors['required']) return 'Bu alan zorunludur';
            if (field.errors['maxlength']) return `Maksimum ${field.errors['maxlength'].requiredLength} karakter olmalıdır`;
        }
        return '';
    }

    addTestHedef(adi: string = 'Mehmet', soyadi: string = 'Örnek', hedefNo: string = '1'): void {
        // Ensure test aidiyat codes are available in both options arrays
        const testAidiyatCodes = ['AIDIYAT1', 'AIDIYAT2'];
        testAidiyatCodes.forEach(code => {
            if (!this.hedefAidiyatKodlariOptions.includes(code)) {
                this.hedefAidiyatKodlariOptions.push(code);
            }
        });

        const testHedef = this.fb.group({
            hedefNo: [hedefNo, Validators.required],
            hedefTip: [HedefTipEnum.Gsm, Validators.required],
            hedefAd: [adi, Validators.required],
            hedefSoyad: [soyadi, Validators.required],
            baslamaTarihi: [new Date(), Validators.required],
            sure: ['30'],
            sureTip: [SureTipEnum.Gun, Validators.required],
            hedefAidiyatKodlari: [testAidiyatCodes, Validators.required],
            canakNo: ['CN-TEST-001']
        });

        this.hedefDetayListesi.push(testHedef);
    }

    private createTestFile(fileName: string): void {
        // Create a simple test PDF file
        const pdfContent = '%PDF-1.4\n%âãÏÓ\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n/Contents 4 0 R\n>>\nendobj\n4 0 obj\n<<\n/Length 44\n>>\nstream\nBT\n/F1 12 Tf\n100 700 Td\n(Test PDF Document) Tj\nET\nendstream\nendobj\ntrailer\n<<\n/Root 1 0 R\n>>\n%%EOF';

        const blob = new Blob([pdfContent], {type: 'application/pdf'});
        this.seciliDosya = new File([blob], fileName, {type: 'application/pdf'});
    }
}
