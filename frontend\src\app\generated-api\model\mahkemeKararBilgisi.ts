/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { MahkemeKararDetay } from './mahkemeKararDetay';


export interface MahkemeKararBilgisi { 
    mahkemeKararTipi?: MahkemeKararBilgisi.MahkemeKararTipiEnum;
    mahkemeKararDetay?: MahkemeKararDetay;
}
export namespace MahkemeKararBilgisi {
    export const MahkemeKararTipiEnum = {
        OnleyiciHakimKarari: 'ONLEYICI_HAKIM_KARARI',
        SinyalBilgiDegerlendirmeKarari: 'SINYAL_BILGI_DEGERLENDIRME_KARARI',
        AboneKutukBilgileriKarari: 'ABONE_KUTUK_BILGILERI_KARARI',
        OnleyiciYaziliEmir: 'ONLEYICI_YAZILI_EMIR',
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'ADLI_HAKIM_KARARI',
        AdliHakimHtsKarari: 'ADLI_HAKIM_HTS_KARARI',
        AdliYaziliEmir: 'ADLI_YAZILI_EMIR',
        AdliKhkYaziliEmir: 'ADLI_KHK_YAZILI_EMIR',
        AdliSavcilikHtsKarari: 'ADLI_SAVCILIK_HTS_KARARI',
        HedefAdSoyadDegistirme: 'HEDEF_AD_SOYAD_DEGISTIRME',
        HedefBilgiDegistirme: 'HEDEF_BILGI_DEGISTIRME',
        MahkemeKoduDegistirme: 'MAHKEME_KODU_DEGISTIRME',
        MahkemeKararBilgiDegistirme: 'MAHKEME_KARAR_BILGI_DEGISTIRME',
        MahkemeAidiyatDegistirme: 'MAHKEME_AIDIYAT_DEGISTIRME',
        OnleyiciSonlandirma: 'ONLEYICI_SONLANDIRMA',
        AdliSonlandirma: 'ADLI_SONLANDIRMA',
        AdliSavcilikSonlandirma: 'ADLI_SAVCILIK_SONLANDIRMA',
        AdliSavcilikYerTespitiSonlandirma: 'ADLI_SAVCILIK_YER_TESPITI_SONLANDIRMA',
        AdliKhkSonlandirma: 'ADLI_KHK_SONLANDIRMA',
        AdliAskeriHakimKarari: 'ADLI_ASKERI_HAKIM_KARARI',
        AdliAskeriSonlandirma: 'ADLI_ASKERI_SONLANDIRMA',
        AdliAskeriSavcilikSonlandirma: 'ADLI_ASKERI_SAVCILIK_SONLANDIRMA',
        CanakNumaraDegistirme: 'CANAK_NUMARA_DEGISTIRME',
        AdliAskeriYerTespitiSonlandirma: 'ADLI_ASKERI_YER_TESPITI_SONLANDIRMA',
        MahkemeSuctipiDegistirme: 'MAHKEME_SUCTIPI_DEGISTIRME'
    } as const;
    export type MahkemeKararTipiEnum = typeof MahkemeKararTipiEnum[keyof typeof MahkemeKararTipiEnum];
}


