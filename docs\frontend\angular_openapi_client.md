# OpenAPI Angular Client Generation Guide (Windows 11)

This document explains how to generate an **Angular client** from the backend's OpenAPI specification using **OpenAPI Generator**, and how to integrate it into your frontend project in npm style.

---

## 1. Prerequisites
- Backend REST API exposes OpenAPI spec at:
  ```
  http://localhost:4000/api-docs.yaml
  ```
  (JSON also available at `http://localhost:4000/api-docs`)
- Node.js & npm installed.
- Angular project already set up (`frontend/`).

Install the OpenAPI Generator CLI globally:
```powershell
npm install @openapitools/openapi-generator-cli -g
```

---

## 2. Generate Angular Client
Run the generator from the **frontend project root** in **Command Prompt**:

```cmd
openapi-generator-cli generate `
  -i http://localhost:4000/api-docs.yaml `
  -g typescript-angular `
  -o .\src\app\generated-api
```

This will create the Angular TypeScript SDK directly inside your frontend project under `src/app/generated-api`.

⚠️ **Important:** Every time you run this command, the output folder (`src/app/generated-api`) is cleared and fully regenerated. Any manual changes inside that folder will be lost.

---

## 3. Build Client as npm Package (Optional)
If you prefer to keep the client as a standalone npm package instead of directly in `src/app/`:

```powershell
cd generated-api
npm install
npm run build
npm pack
```

This creates a tarball package like:
```
openapi-angular-client-1.0.0.tgz
```

Then install it into the Angular project:
```powershell
cd frontend
npm install ..\generated-api\openapi-angular-client-1.0.0.tgz
```

---

## 4. Configure Base URL
The generated Angular client uses `Configuration` to set the API base path.

Update your `environment.ts` files:

**environment.ts**
```ts
export const environment = {
  production: false,
  apiBaseUrl: 'http://localhost:4000'
};
```

**environment.prod.ts**
```ts
export const environment = {
  production: true,
  apiBaseUrl: 'https://api.myapp.com'
};
```

Provide configuration in `app.module.ts`:

```ts
import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { HttpClientModule } from '@angular/common/http';
import { ApiModule, Configuration } from './generated-api';
import { environment } from '../environments/environment';

export function apiConfigFactory() {
  return new Configuration({
    basePath: environment.apiBaseUrl
  });
}

@NgModule({
  declarations: [],
  imports: [
    BrowserModule,
    HttpClientModule,
    ApiModule.forRoot(apiConfigFactory)
  ],
  providers: [],
  bootstrap: []
})
export class AppModule {}
```

---

## 5. Example Usage

```ts
import { Component, OnInit } from '@angular/core';
import { UserService } from './generated-api';

@Component({
  selector: 'app-users',
  template: `<ul><li *ngFor="let u of users">{{ u.name }}</li></ul>`
})
export class UsersComponent implements OnInit {
  users: any[] = [];

  constructor(private userService: UserService) {}

  ngOnInit(): void {
    this.userService.getAllUsers().subscribe(data => this.users = data);
  }
}
```

---

## 6. Regeneration Workflow
Whenever backend APIs change:
1. Re-run client generation (`openapi-generator-cli generate ...`).
   - This will delete old generated files and create a fresh set.
2. If using standalone package: build + pack (`npm run build && npm pack`) and reinstall.
3. If generating directly into `src/app/generated-api`, just regenerate and commit.

This keeps the Angular client always in sync with the backend API.

