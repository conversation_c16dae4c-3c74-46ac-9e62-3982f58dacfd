# GitLab CI/CD Tam Otomatik Kurulum Scripti (2024 - <PERSON>)
# Bu script tüm GitLab CI/CD kurulum işlemlerini tamamen otomatize eder
#
# Özellikler:
# - Docker Desktop kontrolü ve GitLab/Runner başlatma
# - GitLab'in hazır olmasını otomatik bekler
# - Root password'ü otomatik alır ve .secrets dizinine kaydeder
# - GitLab API ile otomatik authentication token oluşturur (GitLab 15.10+ yeni sistem)
# - Runner'ı yeni authentication token sistemi ile register eder
# - Deprecated registration token sistemi yerine güncel API kullanır
# - Proje oluşturma talimatları ve otomatik push
# - Tüm önemli bilgileri .secrets dizininde saklar
# - Pipeline'ı otomatik test eder
#
# GitLab Runner Token Sistemi:
# - GitLab 15.6'dan itibaren registration token'lar deprecated
# - GitLab 15.10+ authentication token siste<PERSON> kullanılıyor
# - GitLab 20.0'da registration token'lar tamamen kaldırılacak
# - Bu script güncel authentication token sistemini kullanır
#
# Kullanım: .\setup-gitlab-complete.ps1 [-ProjectName "iym"] [-GitLabUrl "http://localhost:8929"]

param(
    [string]$ProjectName = "iym",
    [string]$GitLabUrl = "http://localhost:8929",
    [string]$RegistryUrl = "http://localhost:5050",
    [string]$ProjectPath = (Get-Location).Path,
    [switch]$SkipVolumeSetup = $false
)

# System.Web assembly'sini yükle (URL encoding için)
Add-Type -AssemblyName System.Web

# Secrets dizinini oluştur
$secretsDir = Join-Path $ProjectPath ".secrets"
if (-not (Test-Path $secretsDir)) {
    New-Item -Path $secretsDir -ItemType Directory -Force | Out-Null
    Write-Host "✅ .secrets dizini oluşturuldu" -ForegroundColor Green
}

Write-Host "🚀 GitLab CI/CD Tam Otomatik Kurulum Başlatılıyor..." -ForegroundColor Green
Write-Host "📁 Proje: $ProjectName" -ForegroundColor Yellow
Write-Host "🌐 GitLab URL: $GitLabUrl" -ForegroundColor Yellow
Write-Host "📦 Registry URL: $RegistryUrl" -ForegroundColor Yellow
Write-Host "📂 Proje Dizini: $ProjectPath" -ForegroundColor Yellow
Write-Host "🔐 Secrets Dizini: $secretsDir" -ForegroundColor Yellow

# 1. Docker Desktop kontrolü
Write-Host "`n🔍 Docker Desktop kontrolü..." -ForegroundColor Cyan
try {
    $dockerVersion = docker --version
    Write-Host "✅ Docker Desktop çalışıyor: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Desktop çalışmıyor! Lütfen Docker Desktop'ı başlatın." -ForegroundColor Red
    exit 1
}

# 2. External volumes kurulumu (isteğe bağlı)
if (-not $SkipVolumeSetup) {
    Write-Host "`n📁 External volumes kurulumu..." -ForegroundColor Cyan
    $baseDir = "C:\gitlab-data"
    $directories = @("config", "logs", "data", "runner-config")
    
    if (-not (Test-Path $baseDir)) {
        New-Item -Path $baseDir -ItemType Directory -Force | Out-Null
        Write-Host "✅ GitLab data dizini oluşturuldu: $baseDir" -ForegroundColor Green
    }
    
    foreach ($dir in $directories) {
        $path = Join-Path $baseDir $dir
        if (-not (Test-Path $path)) {
            New-Item -Path $path -ItemType Directory -Force | Out-Null
        }
    }
    
    # .env dosyası oluştur
    $envFile = Join-Path $PSScriptRoot ".env"
    Set-Content -Path $envFile -Value "GITLAB_HOME=$baseDir"
    Write-Host "✅ External volumes hazırlandı" -ForegroundColor Green
}

# 3. GitLab ve Runner başlatma
Write-Host "`n🐳 GitLab ve GitLab Runner başlatılıyor..." -ForegroundColor Cyan
Set-Location $PSScriptRoot

# Mevcut container'ları durdur
#docker-compose down -v 2>$null

# GitLab ve Runner'ı başlat
docker-compose -f docker-compose.windows.yml up -d

Write-Host "⏳ GitLab'in başlatılmasını bekliyoruz (bu 3-5 dakika sürebilir)..." -ForegroundColor Yellow

# 4. GitLab'in hazır olmasını bekle
$maxAttempts = 60
$attempt = 0
do {    
    $attempt++
    Write-Host "🔄 GitLab durumu kontrol ediliyor... ($attempt/$maxAttempts)" -ForegroundColor Yellow
    Write-Host "📡 Test URL: $GitLabUrl/users/sign_in" -ForegroundColor Gray

    try {
        $response = Invoke-WebRequest -Uri "$GitLabUrl/users/sign_in" -TimeoutSec 10 -ErrorAction SilentlyContinue
        Write-Host "📊 HTTP Status: $($response.StatusCode)" -ForegroundColor Gray
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ GitLab hazır!" -ForegroundColor Green
            break
        }
    } catch {
        Write-Host "⚠️  GitLab henüz hazır değil: $($_.Exception.Message)" -ForegroundColor Gray
        # GitLab henüz hazır değil, devam et
    }

    if ($attempt -eq $maxAttempts) {
        Write-Host "❌ GitLab başlatılamadı! Lütfen manuel kontrol edin." -ForegroundColor Red
        Write-Host "🔍 Troubleshooting:" -ForegroundColor Yellow
        Write-Host "   1. Docker container'ları çalışıyor mu? (docker ps)" -ForegroundColor White
        Write-Host "   2. GitLab loglarını kontrol edin: docker logs gitlab-cicd-web-1" -ForegroundColor White
        Write-Host "   3. Port 8929 açık mı? (netstat -an | findstr 8929)" -ForegroundColor White
        Write-Host "   4. Firewall/antivirus engelliyor mu?" -ForegroundColor White
        exit 1
    }
    Start-Sleep 10
} while ($true)

# 5. Root password alma ve kaydetme
Write-Host "`n🔑 GitLab root password'ü alınıyor..." -ForegroundColor Cyan
try {
    $rootPassword = docker exec gitlab-cicd-web-1 grep 'Password:' /etc/gitlab/initial_root_password | ForEach-Object { $_.Split(':')[1].Trim() }

    if (-not $rootPassword -or $rootPassword.Length -eq 0) {
        Write-Host "⚠️  Root password boş geldi, alternatif yöntem deneniyor..." -ForegroundColor Yellow
        $rootPassword = docker exec gitlab-cicd-web-1 cat /etc/gitlab/initial_root_password | Select-String "Password:" | ForEach-Object { $_.Line.Split(':')[1].Trim() }
    }

    if (-not $rootPassword -or $rootPassword.Length -eq 0) {
        Write-Host "❌ Root password alınamadı!" -ForegroundColor Red
        Write-Host "🔍 Manuel kontrol için:" -ForegroundColor Yellow
        Write-Host "   docker exec gitlab-cicd-web-1 cat /etc/gitlab/initial_root_password" -ForegroundColor White
        exit 1
    }

    # Root password'ü secrets dizinine kaydet
    $passwordFile = Join-Path $secretsDir "initial_root_password"
    Set-Content -Path $passwordFile -Value $rootPassword
    Write-Host "✅ Root Password kaydedildi: $passwordFile" -ForegroundColor Green
    Write-Host "🔑 Root Password: $rootPassword" -ForegroundColor Yellow

} catch {
    Write-Host "❌ Root password alma hatası: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "🔍 Container çalışıyor mu? docker ps | findstr gitlab-cicd-web-1" -ForegroundColor Yellow
    exit 1
}

# 6. Personal Access Token alma (Manuel veya Otomatik)
Write-Host "`n🎫 GitLab API authentication için Personal Access Token gerekli..." -ForegroundColor Cyan

# Önce mevcut token'ı kontrol et
$tokenFile = Join-Path $secretsDir "personal_access_token"
if (Test-Path $tokenFile) {
    $personalToken = Get-Content $tokenFile -Raw
    Write-Host "✅ Mevcut Personal Access Token bulundu" -ForegroundColor Green

    # Token'ı test et
    Write-Host "🔍 Mevcut token test ediliyor..." -ForegroundColor Yellow
    try {
        $personalToken = $personalToken.Trim() -replace "`r", "" -replace "`n", "" -replace "`t", "" -replace " ", ""
        $testHeaders = @{
            "PRIVATE-TOKEN" = $personalToken
            "Content-Type" = "application/json"
        }
        $testResponse = Invoke-RestMethod -Uri "$GitLabUrl/api/v4/user" -Headers $testHeaders -TimeoutSec 10
        Write-Host "✅ Mevcut token geçerli! User: $($testResponse.name)" -ForegroundColor Green
    } catch {
        Write-Host "❌ Mevcut token geçersiz, yeni token gerekli" -ForegroundColor Red
        $personalToken = $null
    }
}

# Eğer token yoksa veya geçersizse, manuel alma talimatları
if (-not $personalToken) {
    Write-Host "🔑 Personal Access Token manuel olarak alınması gerekiyor:" -ForegroundColor Yellow
    Write-Host "" -ForegroundColor White
    Write-Host "📋 ADIM ADIM TALİMATLAR:" -ForegroundColor Cyan
    Write-Host "   1. Web tarayıcısında şu adrese gidin: $GitLabUrl" -ForegroundColor White
    Write-Host "   2. Giriş bilgileri:" -ForegroundColor White
    Write-Host "      👤 Username: root" -ForegroundColor White
    Write-Host "      🔑 Password: $rootPassword" -ForegroundColor White
    Write-Host "   3. Giriş yaptıktan sonra sağ üst köşedeki avatar'a tıklayın" -ForegroundColor White
    Write-Host "   4. 'Edit profile' seçin" -ForegroundColor White
    Write-Host "   5. Sol menüden 'Access tokens' seçin" -ForegroundColor White
    Write-Host "   6. 'Add new token' butonuna tıklayın" -ForegroundColor White
    Write-Host "   7. Token bilgilerini doldurun:" -ForegroundColor White
    Write-Host "      📝 Name: api-automation-token" -ForegroundColor White
    Write-Host "      📅 Expiry date: 1 yıl sonra" -ForegroundColor White
    Write-Host "      ✅ Scopes: api, read_user, read_repository, write_repository, create_runner" -ForegroundColor White
    Write-Host "   8. 'Create personal access token' butonuna tıklayın" -ForegroundColor White
    Write-Host "   9. Oluşturulan token'ı kopyalayın (glpat-xxx formatında)" -ForegroundColor White
    Write-Host "" -ForegroundColor White

    $personalToken = Read-Host "🔑 Personal Access Token'ı buraya yapıştırın (glpat-xxx formatında)"

    if ($personalToken -and $personalToken.Trim().StartsWith("glpat-")) {
        # Token'ı temizle ve test et
        $personalToken = $personalToken.Trim() -replace "`r", "" -replace "`n", "" -replace "`t", "" -replace " ", ""
        Write-Host "🔍 Girilen token test ediliyor..." -ForegroundColor Yellow
        try {
            $testHeaders = @{
                "PRIVATE-TOKEN" = $personalToken
                "Content-Type" = "application/json"
            }
            $testResponse = Invoke-RestMethod -Uri "$GitLabUrl/api/v4/user" -Headers $testHeaders -TimeoutSec 10
            Write-Host "✅ Token geçerli! User: $($testResponse.name)" -ForegroundColor Green

            # Token'ı kaydet
            Set-Content -Path $tokenFile -Value $personalToken
            Write-Host "✅ Personal Access Token kaydedildi" -ForegroundColor Green

        } catch {
            Write-Host "❌ Token geçersiz: $($_.Exception.Message)" -ForegroundColor Red
            $personalToken = $null
        }
    } else {
        Write-Host "❌ Geçersiz token formatı! Token 'glpat-' ile başlamalıdır." -ForegroundColor Red
        $personalToken = $null
    }
}

# 7. Runner authentication token oluşturma (GitLab 15.10+ yeni sistem)
Write-Host "`n🏃 Runner authentication token oluşturuluyor..." -ForegroundColor Cyan

if ($personalToken) {
    try {
        # Personal token'ı temizle ve validate et
        $personalToken = $personalToken.Trim() -replace "`r", "" -replace "`n", "" -replace "`t", "" -replace " ", ""
        
        # Token format kontrolü
        if (-not $personalToken.StartsWith("glpat-")) {
            throw "Personal Access Token 'glpat-' prefix ile başlamalıdır. Lütfen GitLab UI'dan aldığınız token'ı kullanın."
        }
        
        if ($personalToken.Length -lt 20) {
            throw "Personal Access Token çok kısa. Lütfen tam token'ı kopyaladığınızdan emin olun."
        }
        
        # Token'ı loglarda gösterme (güvenlik için)
        $maskedToken = $personalToken.Substring(0, 8) + "..." + $personalToken.Substring($personalToken.Length - 4)
        Write-Host "🔑 Using Personal Token: $maskedToken" -ForegroundColor Gray
        
        # GitLab API versiyon kontrolü
        $apiVersionCheck = Invoke-RestMethod -Uri "$GitLabUrl/api/v4/version" -Method GET -Headers @{"PRIVATE-TOKEN" = $personalToken} -ErrorAction Stop
        Write-Host "✅ GitLab API erişimi doğrulandı - Versiyon: $($apiVersionCheck.version)" -ForegroundColor Green
        
        # Tarih ve saat formatını ayarlayalım (örn. 2025-09-03_03-15-00)
        $datetimeSuffix = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"

        # Yeni runner authentication token oluştur (POST /user/runners API)
        $runnerTokenData = @{
            runner_type = "instance_type"
            description = "Auto-created runner for $ProjectName-$datetimeSuffix"
            tag_list = "docker,maven,auto"  # Comma-separated string format
            run_untagged = $true
            access_level = "not_protected"
        } | ConvertTo-Json

        $runnerHeaders = @{
            "PRIVATE-TOKEN" = $personalToken
            "Content-Type" = "application/json"
        }

        Write-Host "🔄 GitLab API ile runner oluşturuluyor..." -ForegroundColor Yellow
        Write-Host "📡 API URL: $GitLabUrl/api/v4/user/runners" -ForegroundColor Gray
        Write-Host "📋 Runner Data: $runnerTokenData" -ForegroundColor Gray

        $runnerResponse = Invoke-RestMethod -Uri "$GitLabUrl/api/v4/user/runners" -Method POST -Body $runnerTokenData -Headers $runnerHeaders -Verbose
        $runnerToken = $runnerResponse.token
        $runnerId = $runnerResponse.id

        # Runner token'ı secrets dizinine kaydet
        $runnerTokenFile = Join-Path $secretsDir "runner_token"
        Set-Content -Path $runnerTokenFile -Value $runnerToken

        # Runner ID'yi de kaydet
        $runnerIdFile = Join-Path $secretsDir "runner_id"
        Set-Content -Path $runnerIdFile -Value $runnerId

        Write-Host "✅ Runner authentication token otomatik oluşturuldu ve kaydedildi" -ForegroundColor Green
        Write-Host "🆔 Runner ID: $runnerId" -ForegroundColor Yellow

    } catch {
        Write-Host "❌ Runner authentication token oluşturulamadı - DETAYLI HATA LOGLARI:" -ForegroundColor Red
        Write-Host "🔍 Exception Type: $($_.Exception.GetType().FullName)" -ForegroundColor Yellow
        Write-Host "📝 Exception Message: $($_.Exception.Message)" -ForegroundColor Yellow

        # New-line karakteri hatası için özel çözüm
        if ($_.Exception.Message -like "*New-line characters are not allowed*") {
            Write-Host "🚨 ÖZEL ÇÖZÜM - Token Format Hatası:" -ForegroundColor Red
            Write-Host "   💡 Personal Access Token'da yeni satır veya boşluk karakteri var!" -ForegroundColor Yellow
            Write-Host "   🔧 Lütfen token'ı şu şekilde temizleyin:" -ForegroundColor Cyan
            Write-Host "      1. GitLab UI'dan token'ı kopyalayın" -ForegroundColor White
            Write-Host "      2. Notepad veya başka bir düz metin editöründe yapıştırın" -ForegroundColor White
            Write-Host "      3. Tüm boşlukları ve yeni satırları silin" -ForegroundColor White
            Write-Host "      4. Token'ın 'glpat-' ile başladığından emin olun" -ForegroundColor White
            Write-Host "      5. Tamamını tekrar kopyalayın" -ForegroundColor White
        }

        if ($_.Exception.Response) {
            Write-Host "🌐 HTTP Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Yellow
            Write-Host "📝 HTTP Status Description: $($_.Exception.Response.StatusDescription)" -ForegroundColor Yellow

            try {
                # PowerShell 7+ için HttpResponseMessage
                if ($_.Exception.Response.Content) {
                    $responseBody = $_.Exception.Response.Content.ReadAsStringAsync().Result
                    Write-Host "📄 Response Body: $responseBody" -ForegroundColor Yellow
                } else {
                    Write-Host "📄 Response Body: Boş response" -ForegroundColor Yellow
                }
            } catch {
                Write-Host "⚠️ Response body okunamadı: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }

        if ($_.ErrorDetails) {
            Write-Host "🔍 Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Yellow
        }

        Write-Host "🔧 Troubleshooting:" -ForegroundColor Cyan
        Write-Host "   1. Personal Access Token geçerli mi? (glpat- ile başlamalı)" -ForegroundColor White
        Write-Host "   2. Token'da 'create_runner' scope'u var mı?" -ForegroundColor White
        Write-Host "   3. GitLab versiyonu /user/runners API'sini destekliyor mu?" -ForegroundColor White
        Write-Host "   4. API endpoint'e erişim var mı?" -ForegroundColor White
        Write-Host "   5. Token'da boşluk veya yeni satır karakteri var mı?" -ForegroundColor White

        $runnerToken = $null
    }
}

# Manuel token alma (fallback) - Yeni workflow için güncellenmiş talimatlar
if (-not $runnerToken) {
    Write-Host "⚠️  Runner authentication token'i manuel olarak alınması gerekiyor:" -ForegroundColor Yellow
    Write-Host "   📋 YENİ WORKFLOW (GitLab 15.10+) - Authentication Token Kullanımı:" -ForegroundColor Cyan
    Write-Host "   1. $GitLabUrl/admin/runners adresine gidin" -ForegroundColor White
    Write-Host "   2. Username: root, Password: $rootPassword ile giriş yapın" -ForegroundColor White
    Write-Host "   3. 'New instance runner' butonuna tıklayın" -ForegroundColor White
    Write-Host "   4. Runner açıklaması girin: 'Manual runner for $ProjectName'" -ForegroundColor White
    Write-Host "   5. Tags ekleyin: docker,maven,auto" -ForegroundColor White
    Write-Host "   6. 'Run untagged jobs' seçeneğini işaretleyin" -ForegroundColor White
    Write-Host "   7. 'Create runner' butonuna tıklayın" -ForegroundColor White
    Write-Host "   8. Oluşturulan runner authentication token'i kopyalayın (glrt-xxx formatında)" -ForegroundColor White
    Write-Host "" -ForegroundColor White
    Write-Host "   ⚠️  NOT: Eski registration token sistemi kullanmayın!" -ForegroundColor Red
    Write-Host "   ✅ Sadece 'glrt-' ile başlayan authentication token'ları kullanın" -ForegroundColor Green

    $runnerToken = Read-Host "🔑 Runner authentication token'i buraya yapıştırın (glrt-xxx formatında)"

    if ($runnerToken) {
        # Token formatını kontrol et
        if ($runnerToken.StartsWith("glrt-")) {
            $runnerTokenFile = Join-Path $secretsDir "runner_token"
            Set-Content -Path $runnerTokenFile -Value $runnerToken
            Write-Host "✅ Runner authentication token kaydedildi" -ForegroundColor Green
        } else {
            Write-Host "❌ Geçersiz token formatı! Token 'glrt-' ile başlamalıdır." -ForegroundColor Red
            Write-Host "🔄 Lütfen GitLab UI'dan yeni authentication token alın." -ForegroundColor Yellow
            $runnerToken = $null
        }
    }
}

# 8. GitLab Runner register etme (Yeni Authentication Token Sistemi)
if ($runnerToken) {
    Write-Host "`n🏃 GitLab Runner register ediliyor (Authentication Token ile)..." -ForegroundColor Cyan

    # Token formatını kontrol et
    if (-not $runnerToken.StartsWith("glrt-")) {
        Write-Host "❌ Geçersiz token formatı! Authentication token 'glrt-' ile başlamalıdır." -ForegroundColor Red
        Write-Host "🔄 Lütfen GitLab UI'dan yeni authentication token alın." -ForegroundColor Yellow
    } else {
        # Yeni authentication token sistemi ile register et
        # NOT: --locked, --access-level, --run-untagged, --tag-list parametreleri
        # authentication token ile kullanılamaz (GitLab 15.6+ kısıtlaması)
        $registerResult = docker exec gitlab-cicd-runner-1 gitlab-runner register `
            --non-interactive `
            --url "http://web/" `
            --token "$runnerToken" `
            --request-concurrency="6" `
            --executor "docker" `
            --docker-image "maven:3.9.6-eclipse-temurin-17" `
            --docker-privileged="true" `
            --docker-volumes "/var/run/docker.sock:/var/run/docker.sock" `
            --docker-network-mode="gitlab-cicd_gitlab_network" `
            --description "Auto-registered runner for $ProjectName"

        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ GitLab Runner başarıyla register edildi (Authentication Token ile)!" -ForegroundColor Green
            Write-Host "📋 Runner konfigürasyonu GitLab UI'dan yönetilecek" -ForegroundColor Yellow

            # Runner bilgilerini kaydet
            $runnerInfo = @{
                "registered_at" = (Get-Date).ToString()
                "token" = $runnerToken
                "description" = "Auto-registered runner for $ProjectName"
                "executor" = "docker"
                "docker_image" = "maven:3.9.6-eclipse-temurin-17"
                "registration_method" = "authentication_token"
                "gitlab_version_note" = "GitLab 15.10+ new workflow"
                "configuration_note" = "Tags, access level, and other settings managed via GitLab UI"
            } | ConvertTo-Json

            $runnerInfoFile = Join-Path $secretsDir "runner_info.json"
            Set-Content -Path $runnerInfoFile -Value $runnerInfo

            Write-Host "💡 Runner ayarları (tags, access level, vb.) GitLab UI'dan yapılandırılabilir:" -ForegroundColor Cyan
            Write-Host "   🔗 $GitLabUrl/admin/runners" -ForegroundColor White

        } else {
            Write-Host "❌ Runner register edilemedi!" -ForegroundColor Red
            Write-Host "🔍 Olası nedenler:" -ForegroundColor Yellow
            Write-Host "   • Authentication token geçersiz veya süresi dolmuş" -ForegroundColor White
            Write-Host "   • GitLab bağlantı sorunu" -ForegroundColor White
            Write-Host "   • Runner zaten kayıtlı" -ForegroundColor White
            Write-Host "📋 Çözüm: GitLab UI'dan yeni authentication token alın" -ForegroundColor Yellow
        }
    }
} else {
    Write-Host "❌ Runner authentication token bulunamadı, runner register edilemedi!" -ForegroundColor Red
    Write-Host "🔄 Lütfen GitLab UI'dan authentication token alın ve tekrar deneyin." -ForegroundColor Yellow
}

# 9. Proje kurulumu ve Git konfigürasyonu
Write-Host "`n🏗️ Proje kurulumu..." -ForegroundColor Cyan

# Ana proje dizinine dön
Set-Location $ProjectPath

# # # # Git durumunu kontrol et
# # # $gitStatus = git status --porcelain 2>$null
# # # if ($gitStatus) {
# # #     Write-Host "⚠️  Uncommitted değişiklikler var. Commit yapılıyor..." -ForegroundColor Yellow
# # #     git add .
# # #     git commit -m "GitLab CI/CD setup - Auto commit before GitLab integration"
# # # }

# GitLab remote'unu güncelle (credentials ile)
Write-Host "`n🔗 GitLab remote'u güncelleniyor..." -ForegroundColor Cyan
$encodedPassword = [System.Web.HttpUtility]::UrlEncode($rootPassword)
$gitlabRemoteUrl = "http://root:$encodedPassword@localhost:8929/root/$ProjectName.git"

$existingRemotes = git remote 2>$null
if ($existingRemotes -contains "gitlab") {
    git remote set-url gitlab $gitlabRemoteUrl
    Write-Host "✅ GitLab remote güncellendi" -ForegroundColor Green
} else {
    git remote add gitlab $gitlabRemoteUrl
    Write-Host "✅ GitLab remote eklendi" -ForegroundColor Green
}

# Remote bilgilerini kaydet
$remoteInfo = @{
    "gitlab_url" = $GitLabUrl
    "project_url" = "$GitLabUrl/root/$ProjectName"
    "clone_url" = $gitlabRemoteUrl
    "registry_url" = $RegistryUrl
    "setup_date" = (Get-Date).ToString()
} | ConvertTo-Json

$remoteInfoFile = Join-Path $secretsDir "project_info.json"
Set-Content -Path $remoteInfoFile -Value $remoteInfo

# 10. .gitlab-ci.yml dosyasını kopyala
Write-Host "`n📋 Pipeline konfigürasyonu hazırlanıyor..." -ForegroundColor Cyan
if (-not (Test-Path ".gitlab-ci.yml")) {
    $ciConfigSource = Join-Path $PSScriptRoot "gitlab-ci.yml.example"
    if (Test-Path $ciConfigSource) {
        Copy-Item $ciConfigSource ".gitlab-ci.yml"
        git add .gitlab-ci.yml
        git commit -m "Add GitLab CI/CD pipeline configuration"
        Write-Host "✅ Pipeline konfigürasyonu eklendi!" -ForegroundColor Green
    } else {
        Write-Host "⚠️  gitlab-ci.yml.example dosyası bulunamadı" -ForegroundColor Yellow
    }
} else {
    Write-Host "✅ .gitlab-ci.yml zaten mevcut" -ForegroundColor Green
}

# 11. GitLab'da proje oluşturma (API ile otomatik)
Write-Host "`n🏗️ GitLab'da proje oluşturuluyor..." -ForegroundColor Cyan

if ($personalToken) {
    # Önce projenin var olup olmadığını kontrol et
    Write-Host "🔍 Mevcut proje kontrol ediliyor..." -ForegroundColor Yellow
    try {
        $personalToken = $personalToken.Trim() -replace "`r", "" -replace "`n", "" -replace "`t", "" -replace " ", ""
        
        # Token format kontrolü
        if (-not $personalToken.StartsWith("glpat-")) {
            throw "Personal Access Token 'glpat-' prefix ile başlamalıdır."
        }
        
        $maskedToken = $personalToken.Substring(0, 8) + "..." + $personalToken.Substring($personalToken.Length - 4)
        Write-Host "🔑 Using Personal Token: $maskedToken" -ForegroundColor Gray
        
        $projectHeaders = @{
            "PRIVATE-TOKEN" = $personalToken
            "Content-Type" = "application/json"
        }

        # Proje var mı kontrol et (root namespace'inde)
        $existingProject = Invoke-RestMethod -Uri "$GitLabUrl/api/v4/projects/root%2F$ProjectName" -Headers $projectHeaders -ErrorAction SilentlyContinue
        Write-Host "✅ Proje zaten mevcut: $($existingProject.name)" -ForegroundColor Green
        Write-Host "🔗 Proje URL: $($existingProject.web_url)" -ForegroundColor Gray
        $projectCreated = $true

    } catch {
        if ($_.Exception.Response.StatusCode -eq 404) {
            Write-Host "📋 Proje bulunamadı, yeni proje oluşturuluyor..." -ForegroundColor Yellow

            # Yeni proje oluştur
            $projectData = @{
                name = $ProjectName
                path = $ProjectName.ToLower()
                description = "Auto-created project for CI/CD automation"
                visibility = "private"
                initialize_with_readme = $false
                issues_enabled = $true
                merge_requests_enabled = $true
                wiki_enabled = $true
                snippets_enabled = $true
                container_registry_enabled = $true
                builds_enabled = $true
            } | ConvertTo-Json

            try {
                Write-Host "🔄 API ile proje oluşturuluyor..." -ForegroundColor Yellow
                Write-Host "📡 API URL: $GitLabUrl/api/v4/projects" -ForegroundColor Gray
                Write-Host "📋 Project Data: $projectData" -ForegroundColor Gray

                $newProject = Invoke-RestMethod -Uri "$GitLabUrl/api/v4/projects" -Method POST -Body $projectData -Headers $projectHeaders

                Write-Host "✅ Proje başarıyla oluşturuldu!" -ForegroundColor Green
                Write-Host "🆔 Project ID: $($newProject.id)" -ForegroundColor Yellow
                Write-Host "📝 Project Name: $($newProject.name)" -ForegroundColor Yellow
                Write-Host "🔗 Project URL: $($newProject.web_url)" -ForegroundColor Yellow
                Write-Host "📂 Clone URL: $($newProject.http_url_to_repo)" -ForegroundColor Yellow

                # Proje bilgilerini kaydet
                $projectInfo = @{
                    "id" = $newProject.id
                    "name" = $newProject.name
                    "path" = $newProject.path
                    "web_url" = $newProject.web_url
                    "http_url_to_repo" = $newProject.http_url_to_repo
                    "ssh_url_to_repo" = $newProject.ssh_url_to_repo
                    "created_at" = $newProject.created_at
                    "visibility" = $newProject.visibility
                } | ConvertTo-Json

                $projectInfoFile = Join-Path $secretsDir "project_details.json"
                Set-Content -Path $projectInfoFile -Value $projectInfo

                $projectCreated = $true

            } catch {
                Write-Host "❌ Proje oluşturulamadı - API Hatası:" -ForegroundColor Red
                Write-Host "🔍 Exception: $($_.Exception.Message)" -ForegroundColor Yellow

                # New-line karakteri hatası için özel çözüm
                if ($_.Exception.Message -like "*New-line characters are not allowed*") {
                    Write-Host "🚨 ÖZEL ÇÖZÜM - Token Format Hatası:" -ForegroundColor Red
                    Write-Host "   💡 Personal Access Token'da yeni satır veya boşluk karakteri var!" -ForegroundColor Yellow
                    Write-Host "   🔧 Lütfen token'ı şu şekilde temizleyin:" -ForegroundColor Cyan
                    Write-Host "      1. GitLab UI'dan token'ı kopyalayın" -ForegroundColor White
                    Write-Host "      2. Notepad veya başka bir düz metin editöründe yapıştırın" -ForegroundColor White
                    Write-Host "      3. Tüm boşlukları ve yeni satırları silin" -ForegroundColor White
                    Write-Host "      4. Token'ın 'glpat-' ile başladığından emin olun" -ForegroundColor White
                    Write-Host "      5. Tamamını tekrar kopyalayın" -ForegroundColor White
                }

                if ($_.Exception.Response) {
                    Write-Host "🌐 HTTP Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Yellow

                    try {
                        if ($_.Exception.Response.Content) {
                            $responseBody = $_.Exception.Response.Content.ReadAsStringAsync().Result
                            Write-Host "📄 Response: $responseBody" -ForegroundColor Yellow
                        }
                    } catch {
                        Write-Host "⚠️ Response okunamadı" -ForegroundColor Yellow
                    }
                }

                $projectCreated = $false
            }
        } else {
            Write-Host "❌ Proje kontrol hatası: $($_.Exception.Message)" -ForegroundColor Red
            $projectCreated = $false
        }
    }
} else {
    Write-Host "⚠️  Personal Access Token bulunamadı, manuel proje oluşturma gerekli" -ForegroundColor Yellow
    $projectCreated = $false
}

# Manuel proje oluşturma talimatları (fallback)
if (-not $projectCreated) {
    Write-Host "`n📝 Manuel proje oluşturma talimatları:" -ForegroundColor Yellow
    Write-Host "   1. $GitLabUrl adresine gidin" -ForegroundColor White
    Write-Host "   2. Username: root, Password: $rootPassword ile giriş yapın" -ForegroundColor White
    Write-Host "   3. 'New project' > 'Create blank project' seçin" -ForegroundColor White
    Write-Host "   4. Project name: $ProjectName" -ForegroundColor White
    Write-Host "   5. Project slug: $($ProjectName.ToLower())" -ForegroundColor White
    Write-Host "   6. Visibility Level: Private" -ForegroundColor White
    Write-Host "   7. ⚠️  'Initialize repository with a README' UNCHECKED ❌" -ForegroundColor Red
    Write-Host "   8. 'Create project' butonuna tıklayın" -ForegroundColor White

    $createChoice = Read-Host "`n🤔 Proje oluşturuldu mu? Push yapmak için (y/n)"
} else {
    Write-Host "`n✅ Proje hazır, kod push işlemine geçiliyor..." -ForegroundColor Green
    $createChoice = "y"
}

# 12. GitLab'a push et
if ($createChoice -eq "y" -or $createChoice -eq "Y") {
    Write-Host "`n📤 GitLab'a kod push ediliyor..." -ForegroundColor Cyan

    $currentBranch = git branch --show-current
    Write-Host "📍 Mevcut branch: $currentBranch" -ForegroundColor Yellow

    $pushResult = git push gitlab $currentBranch 2>&1
    $pushExitCode = $LASTEXITCODE

    if ($pushExitCode -eq 0) {
        Write-Host "✅ Kod başarıyla GitLab'a push edildi!" -ForegroundColor Green

        # Push bilgilerini kaydet
        $pushInfo = @{
            "last_push" = (Get-Date).ToString()
            "branch" = $currentBranch
            "status" = "success"
        } | ConvertTo-Json

        $pushInfoFile = Join-Path $secretsDir "last_push.json"
        Set-Content -Path $pushInfoFile -Value $pushInfo

        # 13. Pipeline test et
        Write-Host "`n🔄 Pipeline durumu kontrol ediliyor..." -ForegroundColor Cyan
        Start-Sleep 5

        if ($personalToken) {
            try {
                $cleanToken = $personalToken.Trim() -replace "`r", "" -replace "`n", "" -replace "`t", "" -replace " ", ""
            $pipelineResponse = Invoke-RestMethod -Uri "$GitLabUrl/api/v4/projects/root%2F$ProjectName/pipelines" -Headers @{"PRIVATE-TOKEN" = $cleanToken}
                if ($pipelineResponse.Count -gt 0) {
                    $latestPipeline = $pipelineResponse[0]
                    Write-Host "✅ Pipeline başlatıldı! Status: $($latestPipeline.status)" -ForegroundColor Green
                    Write-Host "🔗 Pipeline URL: $GitLabUrl/root/$ProjectName/-/pipelines/$($latestPipeline.id)" -ForegroundColor Cyan
                }
            } catch {
                Write-Host "⚠️  Pipeline durumu API ile alınamadı" -ForegroundColor Yellow
            }
        }

    } else {
        Write-Host "❌ Push işlemi başarısız!" -ForegroundColor Red
        Write-Host "Hata detayları:" -ForegroundColor Red
        Write-Host $pushResult -ForegroundColor Red
    }
}

# 14. Özet ve sonraki adımlar
Write-Host "`n🎉 GitLab CI/CD Kurulumu Tamamlandı!" -ForegroundColor Green
Write-Host "📋 Kurulum Özeti:" -ForegroundColor Yellow
Write-Host "   • GitLab URL: $GitLabUrl" -ForegroundColor White
Write-Host "   • Registry URL: $RegistryUrl" -ForegroundColor White
Write-Host "   • Root Username: root" -ForegroundColor White
Write-Host "   • Root Password: $rootPassword (kaydedildi: $secretsDir)" -ForegroundColor White
Write-Host "   • Runner Status: $(if($runnerToken){'Registered (Authentication Token)'}else{'Manual setup needed'})" -ForegroundColor White
Write-Host "   • Token System: GitLab 15.10+ Authentication Token (Güncel)" -ForegroundColor Green
Write-Host "   • Project URL: $GitLabUrl/root/$ProjectName" -ForegroundColor White
Write-Host "   • Pipeline URL: $GitLabUrl/root/$ProjectName/-/pipelines" -ForegroundColor White
Write-Host "   • Runner Management: $GitLabUrl/admin/runners" -ForegroundColor White

Write-Host "`n📁 Kaydedilen Bilgiler (.secrets dizini):" -ForegroundColor Yellow
Get-ChildItem $secretsDir | ForEach-Object {
    Write-Host "   • $($_.Name)" -ForegroundColor White
}

Write-Host "`n📝 Sonraki Adımlar:" -ForegroundColor Yellow
Write-Host "   1. Pipeline'ı izleyin: $GitLabUrl/root/$ProjectName/-/pipelines" -ForegroundColor White
Write-Host "   2. Docker imajlarını kontrol edin: $RegistryUrl" -ForegroundColor White
Write-Host "   3. Runner ayarlarını yönetin: $GitLabUrl/admin/runners" -ForegroundColor White
Write-Host "   4. Her commit'te otomatik CI/CD çalışacak!" -ForegroundColor White

Write-Host "`n💡 GitLab Runner Token Sistemi Hakkında:" -ForegroundColor Cyan
Write-Host "   • Bu script GitLab 15.10+ authentication token sistemini kullanır" -ForegroundColor White
Write-Host "   • Eski registration token sistemi deprecated (GitLab 20.0'da kaldırılacak)" -ForegroundColor White
Write-Host "   • Runner ayarları (tags, access level) GitLab UI'dan yönetilir" -ForegroundColor White
Write-Host "   • Authentication token'lar daha güvenli ve izlenebilir" -ForegroundColor White

Write-Host "`n✨ Kurulum başarıyla tamamlandı! Artık güncel CI/CD sisteminiz hazır." -ForegroundColor Green

# Ana dizine geri dön
Set-Location $PSScriptRoot
