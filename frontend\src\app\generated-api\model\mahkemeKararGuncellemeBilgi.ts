/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface MahkemeKararGuncellemeBilgi { 
    mahkemeKararGuncellemeAlanTuru?: MahkemeKararGuncellemeBilgi.MahkemeKararGuncellemeAlanTuruEnum;
    yeniDegeri?: string;
}
export namespace MahkemeKararGuncellemeBilgi {
    export const MahkemeKararGuncellemeAlanTuruEnum = {
        MahkemeKodu: 'MAHKEME_KODU',
        SorusturmaNo: 'SORUSTURMA_NO',
        MahkemeKararNo: 'MAHKEME_KARAR_NO'
    } as const;
    export type MahkemeKararGuncellemeAlanTuruEnum = typeof MahkemeKararGuncellemeAlanTuruEnum[keyof typeof MahkemeKararGuncellemeAlanTuruEnum];
}


